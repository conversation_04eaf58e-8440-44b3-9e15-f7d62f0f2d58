# V1.3 错误处理完善与性能监控 - 使用示例

## 📋 概述

V1.3版本实现了强大的错误处理和性能监控功能，大幅提升了系统的稳定性和可观测性。本文档提供了详细的使用示例。

## 🚀 核心功能演示

### 1. 分层错误处理示例

**场景**: 用户查询项目任务时遇到网络问题

**执行流程**:
```javascript
// 用户输入: "查看okr项目的任务"

// 步骤1: 获取项目列表 - 网络错误
{
  "type": "step_error",
  "stepId": "step-1",
  "toolName": "getProjects",
  "errorType": "execution_error",
  "error": "网络连接超时",
  "retryCount": 0,
  "suggestions": ["请稍后重试，API调用频率过高"]
}

// 自动重试
{
  "type": "step_retry",
  "stepId": "step-1",
  "retryCount": 1,
  "maxRetries": 3,
  "timestamp": 1753708025000
}

// 重试失败，启用降级策略
{
  "type": "step_fallback",
  "stepId": "step-1",
  "fallbackResult": {
    "success": true,
    "data": [
      {"id": "default", "name": "默认项目", "description": "系统默认项目"}
    ],
    "metadata": {
      "fallback": true,
      "source": "default",
      "message": "使用默认项目，请稍后重试获取完整项目列表"
    }
  },
  "originalError": "网络连接超时"
}
```

### 2. 性能监控示例

**实时性能追踪**:
```javascript
// 性能报告
{
  "summary": {
    "totalToolCalls": 25,
    "successRate": 0.88,
    "averageResponseTime": 1350,
    "retryRate": 0.16
  },
  "planGeneration": {
    "averageTime": 2200,
    "samples": 8
  },
  "parameterResolution": {
    "averageTime": 45,
    "successRate": 0.96,
    "totalDynamicReferences": 42
  },
  "errors": {
    "byType": {
      "network_error": 3,
      "validation_error": 1,
      "timeout_error": 2
    },
    "byTool": {
      "getProjects": 2,
      "getTasks": 3,
      "createTask": 1
    }
  }
}
```

### 3. 智能重试机制示例

**指数退避重试**:
```javascript
// 第1次重试 - 延迟1秒
{
  "type": "step_retry",
  "stepId": "step-2",
  "retryCount": 1,
  "maxRetries": 3,
  "delay": 1000
}

// 第2次重试 - 延迟2秒
{
  "type": "step_retry", 
  "stepId": "step-2",
  "retryCount": 2,
  "maxRetries": 3,
  "delay": 2000
}

// 第3次重试 - 延迟4秒
{
  "type": "step_retry",
  "stepId": "step-2", 
  "retryCount": 3,
  "maxRetries": 3,
  "delay": 4000
}
```

## 🔧 错误类型处理

### 1. 参数验证错误（不可重试）

```javascript
// 输入: "创建任务"（缺少必要信息）
{
  "type": "step_error",
  "stepId": "create-task-1",
  "errorType": "validation_error",
  "recoverable": false,
  "error": "缺少必需参数: taskData",
  "suggestions": [
    "请检查输入是否包含必要的信息",
    "任务创建需要提供任务标题和描述"
  ]
}
```

### 2. 网络错误（可重试）

```javascript
// API调用超时
{
  "type": "step_error",
  "stepId": "get-tasks-1",
  "errorType": "execution_error", 
  "recoverable": true,
  "error": "请求超时",
  "retryCount": 2,
  "suggestions": [
    "网络连接不稳定，正在重试",
    "如果问题持续，请检查网络连接"
  ]
}
```

### 3. 权限错误（降级处理）

```javascript
// API密钥无效
{
  "type": "step_error",
  "stepId": "get-projects-1",
  "errorType": "execution_error",
  "error": "unauthorized",
  "suggestions": [
    "请检查API密钥配置",
    "联系管理员确认访问权限"
  ]
}

// 自动降级到默认数据
{
  "type": "step_fallback",
  "stepId": "get-projects-1",
  "fallbackResult": {
    "success": true,
    "data": [...],
    "metadata": {
      "fallback": true,
      "message": "使用缓存数据，请稍后重试"
    }
  }
}
```

## 📊 性能监控场景

### 1. 工具调用性能分析

```javascript
// 监控不同工具的性能表现
const performanceData = {
  "getProjects": {
    "averageTime": 1200,
    "successRate": 0.95,
    "retryRate": 0.08
  },
  "getTasks": {
    "averageTime": 1800,
    "successRate": 0.87,
    "retryRate": 0.18
  },
  "createTask": {
    "averageTime": 2500,
    "successRate": 0.92,
    "retryRate": 0.12
  }
}
```

### 2. 参数解析性能优化

```javascript
// 复杂动态参数解析
const complexParameters = {
  "projectId": "$context.targetProject.id",
  "tasks": "$filter(step1.data, status equals \"pending\")",
  "assignee": "$step.getUserInfo.data.id"
}

// 性能记录
{
  "parameterResolution": {
    "averageTime": 65,  // 复杂参数解析耗时更长
    "dynamicReferences": 3,
    "success": true
  }
}
```

### 3. 执行计划性能追踪

```javascript
// 多步骤执行计划性能
{
  "planId": "complex-plan-1",
  "totalSteps": 4,
  "estimatedTime": 8000,
  "actualTime": 9200,
  "efficiency": 0.87,  // 实际时间/预估时间
  "stepPerformance": [
    {"stepId": "step-1", "estimated": 1500, "actual": 1650},
    {"stepId": "step-2", "estimated": 2000, "actual": 2800},
    {"stepId": "step-3", "estimated": 2500, "actual": 2400},
    {"stepId": "step-4", "estimated": 2000, "actual": 2350}
  ]
}
```

## 🎯 高级用法

### 1. 自定义错误处理策略

```javascript
// 扩展错误处理器
class CustomErrorHandler extends EnhancedErrorHandler {
  static async attemptFallback(step, context) {
    // 自定义降级逻辑
    if (step.toolName === 'customTool') {
      return await this.getCustomFallback(context)
    }
    
    return super.attemptFallback(step, context)
  }
  
  static async getCustomFallback(context) {
    // 实现自定义降级策略
    return {
      success: true,
      data: await this.getCachedData(context),
      metadata: {
        fallback: true,
        source: 'cache'
      }
    }
  }
}
```

### 2. 性能监控告警

```javascript
// 性能阈值监控
function checkPerformanceAlerts(monitor) {
  const report = monitor.getPerformanceReport()
  
  // 成功率告警
  if (report.summary.successRate < 0.8) {
    console.warn('工具调用成功率过低:', report.summary.successRate)
  }
  
  // 响应时间告警
  if (report.summary.averageResponseTime > 3000) {
    console.warn('平均响应时间过长:', report.summary.averageResponseTime)
  }
  
  // 重试率告警
  if (report.summary.retryRate > 0.3) {
    console.warn('重试率过高:', report.summary.retryRate)
  }
}
```

### 3. 动态重试策略调整

```javascript
// 基于历史成功率调整重试策略
function adjustRetryStrategy(step, monitor) {
  const report = monitor.getPerformanceReport()
  const toolSuccessRate = report.errors.byTool[step.toolName] || 0
  
  // 根据工具历史成功率调整最大重试次数
  if (toolSuccessRate > 0.9) {
    step.maxRetries = 2  // 高成功率工具减少重试
  } else if (toolSuccessRate < 0.7) {
    step.maxRetries = 5  // 低成功率工具增加重试
  }
  
  return step
}
```

## 🔍 调试和监控

### 1. 详细的执行日志

```
[INFO] 开始执行计划: plan-001
[PERF] 计划生成耗时: 2.3s
[INFO] 步骤1开始: getProjects
[DEBUG] 参数解析: {"filter": "okr"} (45ms, 1个动态引用)
[ERROR] 工具调用失败: 网络连接超时 (1.2s)
[RETRY] 第1次重试 (延迟1s)
[ERROR] 工具调用失败: 网络连接超时 (1.5s)
[RETRY] 第2次重试 (延迟2s)
[FALLBACK] 启用降级策略: 使用默认项目
[INFO] 步骤1完成: 降级成功 (总耗时5.2s)
[PERF] 工具调用统计: 成功率85%, 平均响应时间1.4s
```

### 2. 实时性能仪表板

```javascript
// 实时性能数据
const dashboard = {
  "currentRequests": 3,
  "averageResponseTime": "1.2s",
  "successRate": "87%",
  "activeRetries": 2,
  "fallbacksUsed": 1,
  "topErrors": [
    {"type": "network_error", "count": 5},
    {"type": "timeout_error", "count": 3}
  ],
  "slowestTools": [
    {"name": "getTasks", "avgTime": "2.1s"},
    {"name": "createTask", "avgTime": "1.8s"}
  ]
}
```

## 🎉 总结

V1.3的错误处理完善与性能监控功能为AI助手提供了：

- **强大的错误恢复能力**: 智能重试和降级策略确保系统稳定运行
- **全面的性能可见性**: 实时监控帮助识别和解决性能问题
- **用户友好的错误体验**: 提供具体建议而非技术错误信息
- **可扩展的监控架构**: 易于添加新的监控指标和错误处理策略

这些功能使得AI助手能够在各种复杂环境下稳定运行，为用户提供更可靠的服务体验。通过持续的性能监控和智能错误处理，系统能够自动适应和优化，不断提升服务质量。

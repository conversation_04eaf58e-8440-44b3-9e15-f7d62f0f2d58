<template>
  <view class="content">
    <!-- 加载中状态 -->
    <z-loading v-if="loading" text="加载中" />

    <!-- 菜单内容 -->
    <template v-else>
      <!-- 没有数据的提示 -->
      <view v-if="sortedMenuItems.length === 0" class="empty-state">
        <text class="empty-text">暂无内容</text>
      </view>

      <!-- 菜单项 -->
      <z-menu-item v-else v-for="(item, index) in sortedMenuItems" :key="index" :item="item" :is-top-level="true" />
    </template>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { getMenuMd } from '@/api/okrMd'
import { router } from '@/utils/tools'

// 菜单项数据
const menuItems = ref([])
// 加载状态
const loading = ref(true)

// 排序后的菜单项，文件夹排在文件之前
const sortedMenuItems = computed(() => {
  // 将数据分为文件夹和文件两组
  const folders = menuItems.value.filter((item) => item.children)
  const files = menuItems.value.filter((item) => !item.children)

  // 文件夹排在前面，文件排在后面
  return [...folders, ...files]
})

// 递归移除空文件夹
function removeEmptyFolders(items) {
  if (!items || !items.length) return []

  return items.filter((item) => {
    // 如果是文件（没有 children 属性），保留
    if (!item.children) return true

    // 递归处理子文件夹
    item.children = removeEmptyFolders(item.children)

    // 如果处理后的子文件夹为空，则过滤掉此文件夹
    return item.children.length > 0
  })
}

// 递归对菜单项进行排序，文件夹排在文件之前
function sortMenuItems(items) {
  if (!items || !items.length) return []

  // 对每个有子项的菜单项进行递归排序
  items.forEach((item) => {
    if (item.children && item.children.length) {
      // 将子项分为文件夹和文件两组
      const folders = item.children.filter((child) => child.children)
      const files = item.children.filter((child) => !child.children)

      // 重新组合，文件夹在前
      item.children = [...folders, ...files]

      // 递归排序子文件夹
      sortMenuItems(item.children)
    }
  })

  return items
}

// 接口返回数据处理函数
function parseMenuFromKeys(keys) {
  const root = []

  // 过滤掉以.md 结尾但没有文件名的文件，以及不是.md 结尾的文件
  const filteredKeys = keys.filter((key) => {
    // 必须以.md 结尾
    if (!key.endsWith('.md')) return false

    // 获取文件名部分
    const parts = key.split('/')
    const fileName = parts[parts.length - 1]

    // 排除只有.md 扩展名没有文件名的情况
    return fileName !== '.md' && fileName.length > 3 // 3 是.md 的长度
  })

  filteredKeys.forEach((key) => {
    const parts = key.split('/').filter((p) => p)

    // 如果只有一部分，说明是第一级的文件
    if (parts.length === 1 && parts[0].endsWith('.md')) {
      // 再次确认文件名不仅仅是.md
      if (parts[0] !== '.md' && parts[0].length > 3) {
        root.push({
          label: parts[0].replace(/\.md$/, ''),
          key,
          isRoot: true,
        })
      }
      return // 处理完成，继续下一个 key
    }

    // 处理多级结构
    let currentLevel = root
    parts.forEach((part, index) => {
      const isFile = part.endsWith('.md')
      // 过滤掉只有扩展名的文件
      if (isFile && (part === '.md' || part.length <= 3)) return

      const existingNode = currentLevel.find((n) => n.label === (isFile ? part.replace(/\.md$/, '') : part))

      if (existingNode) {
        currentLevel = existingNode.children || (existingNode.children = [])
      } else {
        const newNode = {
          label: isFile ? part.replace(/\.md$/, '') : part,
          key: isFile ? key : undefined,
          children: isFile ? undefined : [],
        }
        currentLevel.push(newNode)
        if (!isFile) {
          currentLevel = newNode.children || (newNode.children = [])
        } else {
          currentLevel = []
        }
      }
    })
  })

  // 过滤掉没有内容的文件夹
  const filteredRoot = removeEmptyFolders(root)

  // 排序：文件夹在前，文件在后
  return sortMenuItems(filteredRoot)
}

// 组件挂载时查询菜单数据
onMounted(async () => {
  loading.value = true
  try {
    // 调用 getMenuMd 接口获取数据
    const menuData = await getMenuMd()
    console.log('getMenuMd 接口返回数据：', menuData)

    // 提取所有的 Key 值用于构建菜单
    const keys = menuData.map((item) => item.Key)
    menuItems.value = parseMenuFromKeys(keys)
    console.log('处理后的菜单数据：', menuItems.value)

    // 也可以尝试获取特定路径的数据
    const speakMenuData = await getMenuMd('speak')
    console.log('speak 相关菜单数据：', speakMenuData)
  } catch (error) {
    console.error('获取菜单数据失败：', error)
    uni.showToast({ title: '加载失败', icon: 'none' })
  } finally {
    loading.value = false
  }
})
</script>

<style lang="scss">
.content {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f9fa;

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;

    .empty-text {
      font-size: 32rpx;
      color: #999;
    }
  }
}
</style>

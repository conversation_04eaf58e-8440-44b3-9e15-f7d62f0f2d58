/**
 * uTools 自动化脚本
 * 功能：在当前目录打开命令行并运行 py json_viewer.py
 * 支持 Windows、macOS、Linux 系统
 */

// 获取当前工作目录
function getCurrentDirectory() {
    try {
        // 尝试读取当前文件夹路径
        const currentPath = utools.readCurrentFolderPath();
        if (currentPath) {
            return currentPath;
        }
        
        // 如果无法获取当前文件夹，使用默认路径
        const defaultPath = utools.getPath('userData');
        print(`无法获取当前文件夹路径，使用默认路径: ${defaultPath}`);
        return defaultPath;
    } catch (error) {
        print(`获取目录路径失败: ${error.message}`);
        return null;
    }
}

// Windows 系统执行函数
function runOnWindows(directory) {
    try {
        print(`Windows 系统 - 在目录 ${directory} 中运行 Python 脚本`);
        
        // 方法1: 使用 PowerShell 在指定目录运行
        const powershellCommand = `powershell -Command "cd '${directory}'; python json_viewer.py"`;
        utools.shellOpenExternal(powershellCommand);
        
        // 显示成功通知
        utools.showNotification('Python 脚本已启动', 'json_viewer.py 正在运行中...');
        
        return true;
    } catch (error) {
        print(`Windows 执行失败: ${error.message}`);
        
        // 备用方法: 直接打开命令提示符到指定目录
        try {
            utools.shellOpenExternal(`cmd /k "cd /d "${directory}" && python json_viewer.py"`);
            utools.showNotification('命令行已打开', '请手动运行: python json_viewer.py');
            return true;
        } catch (backupError) {
            print(`备用方法也失败: ${backupError.message}`);
            return false;
        }
    }
}

// macOS 系统执行函数
function runOnMacOS(directory) {
    try {
        print(`macOS 系统 - 在目录 ${directory} 中运行 Python 脚本`);
        
        // 使用 AppleScript 打开终端并执行命令
        const appleScript = `
            tell application "Terminal"
                activate
                do script "cd '${directory}' && python3 json_viewer.py"
            end tell
        `;
        
        runAppleScript(appleScript);
        utools.showNotification('Python 脚本已启动', 'json_viewer.py 正在终端中运行');
        
        return true;
    } catch (error) {
        print(`macOS 执行失败: ${error.message}`);
        
        // 备用方法: 使用 shell 命令
        try {
            utools.shellOpenExternal(`open -a Terminal "${directory}"`);
            utools.showNotification('终端已打开', '请手动运行: python3 json_viewer.py');
            return true;
        } catch (backupError) {
            print(`备用方法也失败: ${backupError.message}`);
            return false;
        }
    }
}

// Linux 系统执行函数
function runOnLinux(directory) {
    try {
        print(`Linux 系统 - 在目录 ${directory} 中运行 Python 脚本`);
        
        // 尝试使用常见的终端模拟器
        const terminals = ['gnome-terminal', 'konsole', 'xterm', 'x-terminal-emulator'];
        
        for (const terminal of terminals) {
            try {
                let command;
                if (terminal === 'gnome-terminal') {
                    command = `${terminal} --working-directory="${directory}" -- bash -c "python3 json_viewer.py; exec bash"`;
                } else if (terminal === 'konsole') {
                    command = `${terminal} --workdir "${directory}" -e bash -c "python3 json_viewer.py; exec bash"`;
                } else {
                    command = `${terminal} -e bash -c "cd '${directory}' && python3 json_viewer.py; exec bash"`;
                }
                
                utools.shellOpenExternal(command);
                utools.showNotification('Python 脚本已启动', `使用 ${terminal} 运行 json_viewer.py`);
                return true;
            } catch (terminalError) {
                print(`尝试 ${terminal} 失败: ${terminalError.message}`);
                continue;
            }
        }
        
        // 如果所有终端都失败，尝试直接执行
        utools.shellOpenExternal(`bash -c "cd '${directory}' && python3 json_viewer.py"`);
        utools.showNotification('Python 脚本已启动', 'json_viewer.py 正在后台运行');
        return true;
        
    } catch (error) {
        print(`Linux 执行失败: ${error.message}`);
        return false;
    }
}

// 检查 Python 环境
function checkPythonEnvironment(directory) {
    try {
        // 检查 json_viewer.py 文件是否存在
        const fs = require('fs');
        const path = require('path');
        const scriptPath = path.join(directory, 'json_viewer.py');
        
        if (!fs.existsSync(scriptPath)) {
            utools.showNotification('文件不存在', 'json_viewer.py 文件未找到');
            print(`Python 脚本文件不存在: ${scriptPath}`);
            return false;
        }
        
        print(`找到 Python 脚本: ${scriptPath}`);
        return true;
    } catch (error) {
        print(`检查 Python 环境失败: ${error.message}`);
        return true; // 即使检查失败也继续执行
    }
}

// 主执行函数
function main() {
    try {
        print('=== uTools Python 脚本启动器 ===');
        print(`入口参数: ${JSON.stringify(ENTER)}`);
        
        // 获取当前目录
        const currentDir = getCurrentDirectory();
        if (!currentDir) {
            utools.showNotification('错误', '无法获取当前目录路径');
            return;
        }
        
        print(`当前工作目录: ${currentDir}`);
        
        // 检查 Python 环境
        if (!checkPythonEnvironment(currentDir)) {
            return;
        }
        
        // 根据操作系统执行相应的命令
        let success = false;
        
        if (utools.isWindows()) {
            success = runOnWindows(currentDir);
        } else if (utools.isMacOS()) {
            success = runOnMacOS(currentDir);
        } else if (utools.isLinux()) {
            success = runOnLinux(currentDir);
        } else {
            print('未知操作系统');
            utools.showNotification('错误', '不支持的操作系统');
            return;
        }
        
        if (success) {
            print('脚本执行成功');
            // 隐藏 uTools 主窗口
            utools.hideMainWindow();
        } else {
            print('脚本执行失败');
            utools.showNotification('执行失败', '无法启动 Python 脚本');
        }
        
    } catch (error) {
        print(`主函数执行失败: ${error.message}`);
        utools.showNotification('执行错误', error.message);
    }
}

// 备用执行函数 - 简化版本
function simpleRun() {
    try {
        print('=== 简化版本执行 ===');
        
        const currentDir = getCurrentDirectory();
        if (!currentDir) {
            utools.showNotification('错误', '无法获取目录');
            return;
        }
        
        // 直接在文件管理器中打开目录
        utools.shellShowItemInFolder(currentDir);
        
        // 显示提示信息
        utools.showNotification('目录已打开', '请在命令行中运行: python json_viewer.py');
        
        // 隐藏主窗口
        utools.hideMainWindow();
        
    } catch (error) {
        print(`简化执行失败: ${error.message}`);
    }
}

// 执行脚本
try {
    main();
} catch (error) {
    print(`主执行流程失败，尝试简化版本: ${error.message}`);
    simpleRun();
}

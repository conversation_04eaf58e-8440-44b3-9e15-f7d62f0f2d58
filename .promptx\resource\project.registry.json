{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-20T02:00:53.398Z", "updatedAt": "2025-08-20T02:00:53.460Z", "resourceCount": 3}, "resources": [{"id": "frontend-workflow", "source": "project", "protocol": "execution", "name": "Frontend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/frontend-developer/execution/frontend-workflow.execution.md", "metadata": {"createdAt": "2025-08-20T02:00:53.425Z", "updatedAt": "2025-08-20T02:00:53.425Z", "scannedAt": "2025-08-20T02:00:53.425Z", "path": "role/frontend-developer/execution/frontend-workflow.execution.md"}}, {"id": "frontend-developer", "source": "project", "protocol": "role", "name": "Frontend Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/frontend-developer/frontend-developer.role.md", "metadata": {"createdAt": "2025-08-20T02:00:53.441Z", "updatedAt": "2025-08-20T02:00:53.441Z", "scannedAt": "2025-08-20T02:00:53.441Z", "path": "role/frontend-developer/frontend-developer.role.md"}}, {"id": "frontend-thinking", "source": "project", "protocol": "thought", "name": "Frontend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/frontend-developer/thought/frontend-thinking.thought.md", "metadata": {"createdAt": "2025-08-20T02:00:53.458Z", "updatedAt": "2025-08-20T02:00:53.458Z", "scannedAt": "2025-08-20T02:00:53.458Z", "path": "role/frontend-developer/thought/frontend-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}
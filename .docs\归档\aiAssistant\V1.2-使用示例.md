# V1.2 动态参数解析功能 - 使用示例

## 📋 概述

V1.2版本实现了强大的动态参数解析功能，支持复杂的多步骤任务执行。本文档提供了详细的使用示例。

## 🚀 核心功能演示

### 1. 基础查询示例

**用户输入**: "查看我的任务"

**执行流程**:
```javascript
// 生成的执行计划
{
  "planId": "plan-001",
  "steps": [
    {
      "stepId": "step-1",
      "toolName": "getTasks",
      "description": "获取任务列表",
      "parameters": {
        "completed": false
      },
      "dependencies": []
    }
  ]
}
```

**结果**: 直接返回用户的未完成任务列表

### 2. 复杂项目查询示例

**用户输入**: "查看okr项目下的未完成任务"

**执行流程**:
```javascript
// 步骤1: 获取项目列表
{
  "stepId": "step-1",
  "toolName": "getProjects",
  "description": "获取项目列表",
  "parameters": {
    "filter": "okr"
  },
  "dependencies": []
}

// 步骤2: 获取项目任务（使用动态参数）
{
  "stepId": "step-2", 
  "toolName": "getTasks",
  "description": "获取项目下的任务",
  "parameters": {
    "projectId": "$context.targetProject.id",  // 动态参数
    "completed": false
  },
  "dependencies": ["step-1"]
}
```

**动态参数解析过程**:
1. 执行步骤1，获取项目列表
2. 系统自动识别并提取"OKR项目"到上下文
3. 解析`$context.targetProject.id`为实际的项目ID
4. 执行步骤2，获取该项目的任务

## 🔧 动态参数类型

### 1. 上下文引用 ($context.key)

```javascript
// 简单引用
"projectId": "$context.targetProject.id"

// 嵌套引用  
"userId": "$context.user.profile.id"
```

### 2. 步骤结果引用 ($step.stepId.path)

```javascript
// 引用前一步骤的结果
"projectName": "$step.step1.data[0].name"

// 引用特定字段
"taskCount": "$step.getTasksStep.total"
```

### 3. 筛选表达式 ($filter)

```javascript
// 按名称筛选
"$filter(step1.data, name contains \"okr\")"

// 按状态筛选
"$filter(step2.tasks, status equals \"pending\")"

// 按前缀筛选
"$filter(step1.projects, name startsWith \"test\")"
```

### 4. 数组操作

```javascript
// 数组索引
"firstProject": "$step.step1.data[0]"

// 数组筛选
"okrProject": "$step.step1.data[name=okr]"
```

## 📊 实际执行示例

### 示例1: 项目任务查询

**输入**: "查看okr项目的任务"

**AI生成的执行计划**:
```json
{
  "analysis": "用户想查看OKR项目下的任务",
  "steps": [
    {
      "toolName": "getProjects",
      "description": "获取项目列表以找到OKR项目",
      "parameters": {
        "filter": "okr"
      },
      "dependencies": [],
      "reasoning": "需要先找到OKR项目的ID"
    },
    {
      "toolName": "getTasks", 
      "description": "获取OKR项目下的任务",
      "parameters": {
        "projectId": "$context.targetProject.id",
        "completed": false
      },
      "dependencies": ["step1"],
      "reasoning": "使用找到的项目ID获取任务"
    }
  ]
}
```

**执行过程**:
1. **步骤1执行**: 调用`getProjects({filter: "okr"})`
2. **上下文更新**: 系统识别出"OKR项目"并存储到`context.targetProject`
3. **参数解析**: `$context.targetProject.id` → `"proj-123"`
4. **步骤2执行**: 调用`getTasks({projectId: "proj-123", completed: false})`
5. **返回结果**: OKR项目下的未完成任务列表

### 示例2: 复杂筛选查询

**输入**: "找出所有包含'重要'关键词的项目"

**执行计划**:
```json
{
  "steps": [
    {
      "toolName": "getProjects",
      "description": "获取所有项目",
      "parameters": {},
      "dependencies": []
    }
  ]
}
```

**后处理筛选**:
```javascript
// 在结果中应用筛选
const filteredProjects = await DynamicParameterResolver.processFilterExpression(
  '$filter(step1.data, name contains "重要")',
  context
)
```

## 🎯 高级用法

### 1. 多层依赖

```javascript
// 步骤1: 获取用户信息
// 步骤2: 根据用户获取项目 (依赖步骤1)
// 步骤3: 根据项目获取任务 (依赖步骤2)
{
  "steps": [
    {
      "stepId": "getUserInfo",
      "toolName": "getUserProfile",
      "parameters": {"userId": "$context.currentUser.id"}
    },
    {
      "stepId": "getProjects", 
      "toolName": "getProjects",
      "parameters": {"ownerId": "$step.getUserInfo.id"},
      "dependencies": ["getUserInfo"]
    },
    {
      "stepId": "getTasks",
      "toolName": "getTasks", 
      "parameters": {"projectId": "$step.getProjects.data[0].id"},
      "dependencies": ["getProjects"]
    }
  ]
}
```

### 2. 条件筛选

```javascript
// 根据项目状态筛选活跃项目
{
  "toolName": "getTasks",
  "parameters": {
    "projectId": "$filter(step1.projects, status equals \"active\")[0].id"
  }
}
```

### 3. 聚合操作

```javascript
// 获取多个项目的任务总数
{
  "toolName": "getTaskStats",
  "parameters": {
    "projectIds": "$step.getProjects.data.map(p => p.id)"
  }
}
```

## 🔍 调试和监控

### 执行日志示例

```
[INFO] 开始执行计划: plan-001
[INFO] 步骤1开始: getProjects
[DEBUG] 参数解析: {"filter": "okr"}
[INFO] 步骤1完成: 找到2个项目
[DEBUG] 上下文更新: targetProject = {id: "proj-123", name: "OKR项目"}
[INFO] 步骤2开始: getTasks  
[DEBUG] 参数解析: {"projectId": "proj-123", "completed": false}
[INFO] 步骤2完成: 找到5个任务
[INFO] 执行完成: 总耗时1.2秒
```

### 错误处理示例

```javascript
// 参数解析错误
{
  "type": "step_error",
  "stepId": "step-2",
  "error": "上下文数据不存在: invalidKey",
  "executionTime": 50
}

// 工具调用错误  
{
  "type": "step_error",
  "stepId": "step-1", 
  "error": "工具执行失败: 项目ID无效",
  "executionTime": 1200
}
```

## 📈 性能优化

### 1. 并行执行

```javascript
// 无依赖的步骤可以并行执行
{
  "steps": [
    {"stepId": "getProjects", "dependencies": []},
    {"stepId": "getUsers", "dependencies": []},
    {"stepId": "getTasks", "dependencies": ["getProjects"]}
  ]
}
```

### 2. 缓存机制

```javascript
// 相同参数的工具调用会被缓存
context.setStepResult("getProjects-cache", result)
```

### 3. 超时控制

```javascript
{
  "stepId": "step-1",
  "toolName": "getTasks",
  "timeout": 5000,  // 5秒超时
  "retryCount": 3   // 最多重试3次
}
```

## 🎉 总结

V1.2的动态参数解析功能为AI助手提供了强大的多步骤任务处理能力：

- **智能参数解析**: 支持复杂的动态参数引用
- **灵活的数据筛选**: 强大的筛选表达式支持
- **真实工具调用**: 直接调用云函数获取真实数据
- **完善的错误处理**: 多层次的错误处理和恢复机制
- **性能优化**: 智能的执行计划和缓存机制

这些功能使得用户可以通过自然语言执行复杂的跨步骤任务，大大提升了AI助手的实用性和智能化水平。

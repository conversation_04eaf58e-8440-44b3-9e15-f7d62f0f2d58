# Flomo AI 分析功能开发需求

## 功能概述

为 `src/pages/aiAssistant/flomo.vue` 界面的 AI 分析按钮添加后端逻辑和接口，实现基于标签的内容智能分析功能。

## 前端实现

- **触发位置**: 页面中的"AI 分析"按钮（`saveDiary` 函数）
- **传递参数**:
  - `content`: 用户输入的语音转文字内容（字符串）
  - `tags`: 标签信息（JSON 字符串格式）

## 后端接口实现

- **文件位置**: `uniCloud-aliyun/cloudfunctions/ai/index.obj.js`
- **接口名称**: 在 `module.exports` 中新增 AI 分析接口
- **参数接收**:
  - `content`: 用户输入的文字内容
  - `tags`: 标签内容

## 核心功能

1. **AI 大模型调用**: 参考现有的 `chatStreamSSE` 函数实现方式
2. **智能内容分析**: 基于标签对用户输入内容进行智能拆分和标签匹配
3. **提示词设计**: 将用户内容和标签内容整合到提示词中

## 提示词设计目标

- **主要作用**: 拆分用户的输入内容，为不同内容片段打上对应的标签
- **分析逻辑**: 根据标签特征，智能识别内容中的不同主题和类别
- **输出格式**: 返回结构化的分析结果，便于前端展示和处理

## 技术实现要点

- 复用现有的 AI 客户端配置和调用逻辑
- 设计专门的提示词模板，整合用户内容和标签信息
- 确保接口返回格式与前端展示需求匹配
- 实现错误处理和异常情况的优雅降级

## 预期效果

用户点击 AI 分析按钮后，系统能够：

1. 接收用户输入内容和标签信息
2. 调用 AI 大模型进行智能分析
3. 返回结构化的内容分析结果
4. 在前端展示分析结果供用户确认和编辑

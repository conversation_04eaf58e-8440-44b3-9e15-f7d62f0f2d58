<template>
  <div class="message-bubble" :class="{ 'user-bubble': isUser, 'status-message': statusMessage }" @click="handleClick">
    <!-- 文本消息 - 支持多种文本类型 -->
    <div v-if="isTextMessage" class="text-content" :class="{ collapsed: isCollapsed }">
      <template v-if="isUser">
        {{ content }}
      </template>
      <div v-else class="md-row">
        <!-- 流式输入时在开头显示闪烁图标 -->
        <i v-if="streaming" class="fas fa-circle streaming-icon" aria-hidden="true"></i>
        <div class="md-content" v-html="renderedContent"></div>
      </div>
      <div v-if="isCollapsed" class="expand-hint">点击展开</div>
    </div>

    <!-- 工具执行结果消息 -->
    <div v-else-if="type === 'tool_result'" class="tool-result-content">
      <div class="tool-result-header" :class="{ success: toolSuccess, error: !toolSuccess }">
        <i :class="['fas', toolSuccess ? 'fa-check-circle' : 'fa-exclamation-circle']"></i>
        <span>{{ toolName }} {{ toolSuccess ? '执行成功' : '执行失败' }}</span>
      </div>
      <div v-if="toolSuccess && toolResult" class="tool-result-data">
        <!-- 工具执行结果直接展示 -->
        <div v-if="toolResult.details" class="tool-result-details">
          <!-- create -->
          <template v-if="toolResult.details.operationType === 'create'">
            <div v-if="toolResult.details.preview && toolResult.details.preview.length" class="task-cards-container">
              <div v-for="(p, i) in toolResult.details.preview" :key="i" class="task-card create-card"
                @click="handleTaskCardClick(p, 'create')">
                <div class="task-card-header">
                  <div class="operation-badge create-badge">
                    <i class="fas fa-plus-circle"></i>
                    <span>{{ getOperationLabel('create') }}</span>
                  </div>
                </div>
                <div class="task-card-body">
                  <div class="task-title">{{ p.title }}</div>

                  <!-- 任务描述/内容 -->
                  <div v-if="p.content || p.description" class="task-description">
                    {{ p.content || p.description }}
                  </div>

                  <!-- 动态信息区域 -->
                  <div class="task-info-grid">
                    <!-- 项目信息 -->
                    <div v-if="p.projectName" class="info-item project-info">
                      <i class="fas fa-folder"></i>
                      <span>{{ p.projectName }}</span>
                    </div>

                    <!-- 优先级 -->
                    <div v-if="p.priority > 0" class="info-item priority-info">
                      <i class="fas fa-flag"></i>
                      <span>{{ getPriorityLabel(p.priority) }}</span>
                    </div>

                    <!-- 任务类型 -->
                    <div v-if="p.type" class="info-item type-info">
                      <i class="fas fa-tag"></i>
                      <span>{{ getTypeLabel(p.type) }}</span>
                    </div>

                    <!-- 权重 -->
                    <div v-if="p.weight !== undefined && p.weight !== null && p.weight !== 0"
                      class="info-item weight-info">
                      <i class="fas fa-weight-hanging"></i>
                      <span>权重 {{ p.weight }}</span>
                    </div>

                    <!-- 提醒设置 -->
                    <div v-if="p.reminder" class="info-item reminder-info">
                      <i class="fas fa-bell"></i>
                      <span>{{ getReminderLabel(p.reminder) }}</span>
                    </div>
                  </div>

                  <!-- 标签 -->
                  <div v-if="p.tagNames && p.tagNames.length" class="task-tags">
                    <span v-for="tag in p.tagNames" :key="tag" class="tag">{{ tag }}</span>
                  </div>

                  <!-- 时间信息 -->
                  <div class="task-meta" v-if="p.startDate || (!p.isAllDay && p.dueDate)">
                    <div v-if="p.startDate" class="meta-item">
                      <i class="fas fa-calendar-alt"></i>
                      <span>{{ p.isAllDay ? '日期：' : '开始：' }}{{ formatTaskDate(p.startDate, p.isAllDay) }}</span>
                    </div>
                    <div v-if="p.dueDate && !p.isAllDay" class="meta-item">
                      <i class="fas fa-calendar-check"></i>
                      <span>截止：{{ formatTaskDate(p.dueDate, p.isAllDay) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <!-- update -->
          <template v-else-if="toolResult.details.operationType === 'update'">
            <div class="task-cards-container">
              <div v-for="(chg, idx) in toolResult.details.changes || []" :key="idx" class="task-card update-card"
                @click="handleTaskCardClick(chg, 'update')">
                <div class="task-card-header">
                  <div class="operation-badge update-badge">
                    <i class="fas fa-edit"></i>
                    <span>{{ getOperationLabel('update') }}</span>
                  </div>
                </div>
                <div class="task-card-body">
                  <div class="task-title">{{ chg.title || '未命名' }}</div>

                  <!-- 显示任务的额外信息（如果有） -->
                  <div v-if="chg.projectName || chg.type || chg.priority !== undefined" class="task-context-info">
                    <span v-if="chg.projectName" class="context-item">
                      <i class="fas fa-folder"></i>{{ chg.projectName }}
                    </span>
                    <span v-if="chg.type" class="context-item">
                      <i class="fas fa-tag"></i>{{ getTypeLabel(chg.type) }}
                    </span>
                    <span v-if="chg.priority > 0" class="context-item">
                      <i class="fas fa-flag"></i>{{ getPriorityLabel(chg.priority) }}
                    </span>
                  </div>

                  <div class="changes-info">
                    <div class="changes-summary">
                      <span class="changes-label">变更字段：</span>
                      <span class="changes-fields">{{ getFieldLabels(chg.changedFields || []).join(', ') }}</span>
                    </div>
                    <div class="diff-list">
                      <div v-for="(d, field) in chg.diff || {}" :key="field" class="diff-item">
                        <span class="field-name">{{ getFieldLabel(field) }}</span>
                        <span class="diff-arrow">→</span>
                        <span class="field-value">{{ formatDiffValue(d.after, field, chg.isAllDay) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <!-- delete -->
          <template v-else-if="toolResult.details.operationType === 'delete'">
            <div v-if="toolResult.details.titles && toolResult.details.titles.length" class="task-cards-container">
              <div v-for="(title, i) in toolResult.details.titles" :key="i" class="task-card delete-card"
                @click="handleTaskCardClick({ title }, 'delete')">
                <div class="task-card-header">
                  <div class="operation-badge delete-badge">
                    <i class="fas fa-trash-alt"></i>
                    <span>{{ getOperationLabel('delete') }}</span>
                  </div>
                </div>
                <div class="task-card-body">
                  <div class="task-title">{{ title }}</div>
                </div>
              </div>
            </div>
          </template>
          <div v-if="toolResult.details.truncated" class="truncated-hint">为保证性能，已部分省略</div>
        </div>
        <div v-if="!toolSuccess && toolError" class="tool-error-message">
          {{ toolError }}
        </div>
      </div>
    </div>
    <!-- 错误消息 -->
    <div v-else-if="type === 'error'" class="error-content">
      <div class="error-header">
        <i class="fas fa-exclamation-triangle"></i>
        <span>处理出错</span>
      </div>
      <div class="error-message">{{ content }}</div>
    </div>
    <!-- 音频消息 -->
    <div v-else-if="type === 'audio'" class="audio-content" @click="toggleAudioPlay">
      <div class="audio-icon">
        <i :class="['fas', isPlaying ? 'fa-pause' : 'fa-play']"></i>
      </div>
      <div class="audio-duration">{{ isPlaying ? formatRemainingTime() : formatDuration(audioDuration) }}</div>
      <div class="text-icon" :class="{
        transcribing: isTranscribing,
        clickable: transcribeResult,
        collapsed: !showTranscribeResult,
      }" @click="transcribeResult ? toggleTranscribeResult($event) : null">
        文
      </div>
      <!-- 转写结果 -->
      <div v-if="transcribeResult && showTranscribeResult" class="transcribe-result">{{ transcribeResult }}</div>
    </div>

    <!-- 任务详情弹窗 -->
    <LTaskDetailModal :visible="showTaskDetailModal" :task-data="selectedTaskData"
      :operation-type="selectedOperationType" @close="handleCloseTaskDetail" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { parseMarkdown } from '@/utils/tools'
import LTaskDetailModal from './l-task-detail-modal.vue'
import dayjs from 'dayjs'

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'text', // 'text' 或 'audio'
  },
  audioUrl: {
    type: String,
    default: '',
  },
  audioDuration: {
    type: Number,
    default: 0, // 音频时长（毫秒）
  },
  isUser: {
    type: Boolean,
    default: false,
  },
  streaming: {
    type: Boolean,
    default: false,
  },
  isCollapsed: {
    type: Boolean,
    default: false,
  },
  onToggle: {
    type: Function,
    default: () => {
      /* no op */
    },
  },
  isTranscribing: {
    type: Boolean,
    default: false,
  },
  transcribeResult: {
    type: String,
    default: '',
  },
  statusMessage: {
    type: Boolean,
    default: false,
  },
  // 工具执行结果相关属性
  toolName: {
    type: String,
    default: '',
  },
  toolSuccess: {
    type: Boolean,
    default: false,
  },
  toolResult: {
    type: Object,
    default: () => null,
  },
  toolError: {
    type: String,
    default: '',
  },
  toolExecutionTime: {
    type: Number,
    default: null,
  },
})

const emit = defineEmits(['toggle'])

// 任务详情弹窗相关状态
const showTaskDetailModal = ref(false)
const selectedTaskData = ref({})
const selectedOperationType = ref('create')

const operationTypeLabel = computed(() => {
  const t = props.toolResult?.details?.operationType
  if (t === 'create') return '创建'
  if (t === 'update') return '更新'
  if (t === 'delete') return '删除'
  if (t === 'query') return '查询'
  return '操作'
})

// 根据工具名称判断操作对象类型
const getObjectType = computed(() => {
  const toolName = props.toolName
  if (toolName && (toolName.includes('Memory') || toolName.includes('memory'))) {
    return '记忆'
  }
  return '任务'
})

// 根据操作类型和对象类型生成操作标签
const getOperationLabel = (operationType) => {
  const objectType = getObjectType.value
  if (operationType === 'create') return `新增${objectType}`
  if (operationType === 'update') return `更新${objectType}`
  if (operationType === 'delete') return `删除${objectType}`
  return `操作${objectType}`
}

// 已取消 ID 展示，留空占位避免引用报错
const displayIds = computed(() => '')

const formatDiffValue = (v, field, isAllDay = false) => {
  if (v === null || v === undefined) return ''

  // 检查是否为时间字段
  if ((field === 'startDate' || field === 'dueDate') && typeof v === 'string') {
    return formatTaskDate(v, isAllDay)
  }

  if (typeof v === 'string') return v
  try {
    return JSON.stringify(v)
  } catch {
    return String(v)
  }
}

const formatTaskDate = (dateStr, isAllDay = false) => {
  if (!dateStr) return ''
  try {
    const date = dayjs(dateStr)
    if (isAllDay) {
      // 全天任务只显示日期：MM-DD 格式
      return date.format('MM-DD')
    } else {
      // 非全天任务显示日期+时间：MM-DD HH:mm 格式
      return date.format('MM-DD HH:mm')
    }
  } catch {
    return dateStr
  }
}

// 优先级标签映射
const getPriorityLabel = (priority) => {
  const priorityMap = {
    1: '低',
    2: '中低',
    3: '中',
    4: '中高',
    5: '高',
  }
  return priorityMap[priority] || `优先级 ${priority}`
}

// 任务类型标签映射
const getTypeLabel = (type) => {
  const typeMap = {
    kr: '关键结果',
    todo: '待办任务',
    habit: '习惯',
    TEXT: '文本任务',
    CHECKLIST: '清单任务',
    NOTE: '笔记任务',
  }
  return typeMap[type] || type
}

// 状态标签映射
const getStatusLabel = (status) => {
  const statusMap = {
    0: '未完成',
    1: '已完成',
    2: '已放弃',
  }
  return statusMap[status] || `状态 ${status}`
}

// 提醒标签映射
const getReminderLabel = (reminder) => {
  if (!reminder) return ''
  if (reminder === '0') return '准时提醒'
  if (reminder.includes('M')) return `提前${reminder.replace('-', '').replace('M', '分钟')}`
  if (reminder.includes('H')) return `提前${reminder.replace('-', '').replace('H', '小时')}`
  if (reminder.includes('D')) return `提前${reminder.replace('-', '').replace('D', '天')}`
  return reminder
}

// 字段名称映射（用于更新操作的字段显示）
const getFieldLabel = (field) => {
  const fieldMap = {
    title: '标题',
    content: '内容',
    description: '描述',
    startDate: '开始时间',
    dueDate: '截止时间',
    priority: '优先级',
    status: '状态',
    projectName: '项目',
    tagNames: '标签',
    weight: '权重',
    type: '类型',
    kind: '种类',
    projectId: '项目ID',
    isAllDay: '全天',
    reminder: '提醒',
  }
  return fieldMap[field] || field
}

// 批量获取字段标签
const getFieldLabels = (fields) => {
  return fields.map((field) => getFieldLabel(field))
}

// 计算属性：判断是否是文本类型消息
const isTextMessage = computed(() => {
  const textTypes = ['text', 'user', 'ai_streaming', 'ai_complete']
  return textTypes.includes(props.type)
})

// 解析后的 Markdown 内容（用于 AI 文本消息）
const renderedContent = computed(() => {
  if (!props.content) return ''
  return parseMarkdown(props.content)
})

// 音频播放状态
const isPlaying = ref(false)
let audioElement = null
let innerAudioContext = null

// 当前播放时间
const currentTime = ref(0)

// 添加转写结果显示状态
const showTranscribeResult = ref(true)

// 在组件挂载时创建音频元素
onMounted(() => {
  if (props.type === 'audio' && props.audioUrl) {
    // #ifdef H5
    audioElement = new Audio(props.audioUrl)

    // 监听播放结束事件
    audioElement.addEventListener('ended', handleAudioEnded)
    // 错误处理
    audioElement.addEventListener('error', handleAudioError)
    // 监听时间更新
    audioElement.addEventListener('timeupdate', handleTimeUpdate)
    // #endif

    // #ifndef H5
    // App 端使用 uni 的音频 API
    innerAudioContext = uni.createInnerAudioContext()
    innerAudioContext.src = props.audioUrl

    // 监听播放结束事件
    innerAudioContext.onEnded(() => {
      handleAudioEnded()
    })

    // 错误处理
    innerAudioContext.onError((res) => {
      console.error('音频播放错误：', res)
      handleAudioError(res)
    })

    // 监听时间更新
    innerAudioContext.onTimeUpdate(() => {
      currentTime.value = innerAudioContext.currentTime * 1000 // 转换为毫秒
    })
    // #endif
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  // #ifdef H5
  if (audioElement) {
    audioElement.pause()
    audioElement.removeEventListener('ended', handleAudioEnded)
    audioElement.removeEventListener('error', handleAudioError)
    audioElement.removeEventListener('timeupdate', handleTimeUpdate)
    audioElement = null
  }
  // #endif

  // #ifndef H5
  if (innerAudioContext) {
    innerAudioContext.stop()
    innerAudioContext.destroy()
    innerAudioContext = null
  }
  // #endif
})

// 处理时间更新
const handleTimeUpdate = () => {
  // #ifdef H5
  if (audioElement) {
    currentTime.value = audioElement.currentTime * 1000 // 转换为毫秒
  }
  // #endif
}

// 格式化剩余时间
const formatRemainingTime = () => {
  const remaining = Math.max(0, props.audioDuration - currentTime.value)
  const seconds = Math.ceil(remaining / 1000)
  return `${seconds}"`
}

// 切换音频播放/暂停
const toggleAudioPlay = () => {
  // #ifdef H5
  if (!audioElement) {
    console.error('音频元素不存在')
    return
  }

  if (isPlaying.value) {
    // 暂停播放
    audioElement.pause()
    isPlaying.value = false
  } else {
    // 播放前先停止所有其他正在播放的音频
    stopAllOtherAudio()

    // 开始播放
    audioElement.play().catch((error) => {
      console.error('播放音频失败：', error)
    })
    isPlaying.value = true
  }
  // #endif

  // #ifndef H5
  if (!innerAudioContext) {
    console.error('音频元素不存在')
    return
  }

  if (isPlaying.value) {
    // 暂停播放
    innerAudioContext.pause()
    isPlaying.value = false
  } else {
    // 播放前先停止所有其他正在播放的音频
    stopAllOtherAudio()

    // 开始播放
    innerAudioContext.play()
    isPlaying.value = true
  }
  // #endif
}

// 音频播放结束处理
const handleAudioEnded = () => {
  isPlaying.value = false
  currentTime.value = 0 // 重置当前时间
}

// 音频错误处理
const handleAudioError = (error) => {
  console.error('音频播放错误：', error)
  isPlaying.value = false
}

// 停止所有其他音频播放
const stopAllOtherAudio = () => {
  // #ifdef H5
  // 创建一个自定义事件，通知其他音频消息停止播放
  document.dispatchEvent(
    new CustomEvent('stop-all-audio', {
      detail: { except: props.audioUrl },
    })
  )
  // #endif

  // #ifndef H5
  // App 端使用 uni 的事件系统
  uni.$emit('stop-all-audio', { except: props.audioUrl })
  // #endif
}

// 监听全局音频停止事件
onMounted(() => {
  // #ifdef H5
  document.addEventListener('stop-all-audio', handleStopAllAudio)
  // #endif

  // #ifndef H5
  uni.$on('stop-all-audio', handleStopAllAudio)
  // #endif
})

onUnmounted(() => {
  // #ifdef H5
  document.removeEventListener('stop-all-audio', handleStopAllAudio)
  // #endif

  // #ifndef H5
  uni.$off('stop-all-audio', handleStopAllAudio)
  // #endif
})

// 处理停止所有音频的事件
const handleStopAllAudio = (event) => {
  // #ifdef H5
  // 如果当前音频不是例外，则停止播放
  if (audioElement && isPlaying.value && event.detail.except !== props.audioUrl) {
    audioElement.pause()
    isPlaying.value = false
  }
  // #endif

  // #ifndef H5
  // App 端处理
  if (innerAudioContext && isPlaying.value && event.except !== props.audioUrl) {
    innerAudioContext.pause()
    isPlaying.value = false
  }
  // #endif
}

// 格式化音频时长
const formatDuration = (duration) => {
  const seconds = Math.floor(duration / 1000)
  return `${seconds}"`
}

// 处理点击事件
const handleClick = () => {
  if (props.type === 'text' && !props.isUser) {
    props.onToggle()
  }
}

// 切换转写结果显示/隐藏
const toggleTranscribeResult = (event) => {
  event.stopPropagation() // 阻止事件冒泡，避免触发音频播放
  showTranscribeResult.value = !showTranscribeResult.value
}

// 处理任务卡片点击事件
const handleTaskCardClick = (taskData, operationType) => {
  selectedTaskData.value = taskData
  selectedOperationType.value = operationType
  showTaskDetailModal.value = true
}

// 关闭任务详情弹窗
const handleCloseTaskDetail = () => {
  showTaskDetailModal.value = false
  selectedTaskData.value = {}
}
</script>

<style lang="scss" scoped>
.message-bubble {
  max-width: 70%;
  border-radius: 12px;
  padding: 10px 15px;
  background-color: var(--color-white);
  color: var(--color-gray-800);
  word-break: break-word;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);

  &.user-bubble {
    background-color: var(--color-primary-light);
    color: white;
    border-top-right-radius: 4px;
    border: 1px solid transparent;
  }

  &:not(.user-bubble) {
    border-top-left-radius: 4px;
  }

  &.status-message {
    background-color: #f0f8ff;
    color: #666;
    border-color: #e0e8ef;
    font-style: italic;

    &::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--color-primary-light);
      margin-right: 5px;
      animation: status-pulse 1.2s infinite;
      vertical-align: middle;
    }
  }

  // 音频消息样式
  .audio-content {
    display: flex;
    align-items: center;
    min-width: 120px;
    cursor: pointer;
    justify-content: space-between;
    padding: 0 4px;
    position: relative;
    flex-wrap: wrap;

    .text-icon {
      font-size: 14px;
      color: var(--color-gray-600);
      margin-left: 8px;

      &.transcribing {
        animation: transcribing 1s infinite;
      }

      &.clickable {
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }

      &.collapsed {
        opacity: 0.6;
      }
    }

    .transcribe-result {
      width: 100%;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid var(--color-gray-200);
      font-size: 14px;
      color: var(--color-gray-700);
      word-break: break-all;
      cursor: default;
      white-space: pre-line;
    }

    .audio-icon {
      margin-right: 12px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: var(--color-gray-700);
    }

    .audio-duration {
      font-size: 14px;
      color: var(--color-gray-700);
      min-width: 36px;
      text-align: right;
    }
  }

  .text-content {
    word-break: break-all;
    white-space: pre-wrap;

    .md-row {
      display: flex;
      align-items: flex-start;
      gap: 6px;
    }

    .streaming-icon {
      font-size: 10px;
      color: var(--color-primary);
      margin-top: 6px;
      animation: icon-blink 1.2s ease-in-out infinite;
    }

    @keyframes icon-blink {
      0%,
      100% {
        opacity: 1;
        transform: scale(1);
      }

      50% {
        opacity: 0.3;
        transform: scale(0.85);
      }
    }

    &.collapsed {
      max-height: 60px;
      overflow: hidden;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: linear-gradient(to bottom, transparent, #fff);
      }
    }

    .expand-hint {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      color: #999;
      font-size: 12px;
      padding: 4px 0;
    }
  }
}

.user-bubble .audio-content {
  .audio-icon {
    color: var(--color-primary-dark);
  }

  .audio-duration {
    color: var(--color-primary-dark);
  }
}

@keyframes transcribing {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.5;
    transform: scale(0.95);
  }
}

/* 旧版光标已移除 */

@keyframes status-pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 工具执行结果消息样式 */
.tool-result-content {
  .tool-result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 500;

    &.success {
      color: var(--color-success);
    }

    &.error {
      color: var(--color-danger);
    }

    i {
      font-size: 16px;
    }
  }

  .tool-result-data {
    .tool-message {
      margin-bottom: 6px;
      line-height: 1.5;
      color: var(--color-gray-700);
    }

    .tool-data-summary {
      padding: 6px 10px;
      background-color: var(--color-primary-light);
      border-radius: 4px;
      font-size: 13px;
      color: var(--color-primary-dark);
      margin-bottom: 6px;
    }

    .tool-execution-time {
      font-size: 12px;
      color: var(--color-gray-500);
    }

    .tool-result-details {
      margin-top: 8px;

      .task-cards-container {
        margin-top: 8px;
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .task-card {
        border-radius: 8px;
        padding: 12px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        cursor: pointer;

        &.create-card {
          background: #f0f9f4;
          border-left: 4px solid var(--color-success);

          &:hover {
            background: #e6f7ea;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }

        &.update-card {
          background: #fefbf0;
          border-left: 4px solid var(--color-warning);

          &:hover {
            background: #fef7e0;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }

        &.delete-card {
          background: #fef2f2;
          border-left: 4px solid var(--color-danger);

          &:hover {
            background: #fee2e2;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .task-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .operation-badge {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          font-weight: 500;
          padding: 4px 8px;
          border-radius: 12px;

          &.create-badge {
            background: var(--color-success);
            color: white;
          }

          &.update-badge {
            background: var(--color-warning);
            color: white;
          }

          &.delete-badge {
            background: var(--color-danger);
            color: white;
          }

          i {
            font-size: 11px;
          }
        }


      }

      .task-card-body {
        .task-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--color-gray-800);
          line-height: 1.4;
          margin-bottom: 8px;
        }

        .task-description {
          font-size: 12px;
          color: var(--color-gray-600);
          line-height: 1.4;
          margin-bottom: 8px;
          padding: 6px 8px;
          background: rgba(0, 0, 0, 0.03);
          border-radius: 4px;
        }

        .task-info-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          margin-bottom: 8px;

          .info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.8);

            i {
              font-size: 9px;
            }

            &.project-info {
              color: var(--color-primary);
              background: rgba(59, 130, 246, 0.1);
            }

            &.priority-info {
              color: var(--color-warning);
              background: rgba(245, 158, 11, 0.1);
            }

            &.type-info {
              color: var(--color-info);
              background: rgba(14, 165, 233, 0.1);
            }

            &.weight-info {
              color: var(--color-success);
              background: rgba(34, 197, 94, 0.1);
            }

            &.status-info {
              color: var(--color-gray-600);
              background: rgba(107, 114, 128, 0.1);
            }

            &.allday-info {
              color: var(--color-purple);
              background: rgba(147, 51, 234, 0.1);
            }

            &.reminder-info {
              color: var(--color-orange);
              background: rgba(249, 115, 22, 0.1);
            }
          }
        }

        .task-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
          margin-bottom: 8px;

          .tag {
            font-size: 10px;
            padding: 2px 6px;
            background: var(--color-primary);
            color: white;
            border-radius: 8px;
            line-height: 1.2;
          }
        }

        .task-meta {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 11px;
            color: var(--color-gray-600);

            i {
              width: 12px;
              font-size: 10px;
              color: var(--color-gray-500);
            }
          }
        }

        .task-context-info {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 8px;
          font-size: 11px;

          .context-item {
            display: flex;
            align-items: center;
            gap: 4px;
            color: var(--color-gray-600);
            padding: 2px 6px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 8px;

            i {
              font-size: 9px;
            }
          }
        }

        .changes-info {
          .changes-summary {
            margin-bottom: 8px;
            font-size: 12px;

            .changes-label {
              color: var(--color-gray-600);
              font-weight: 500;
            }

            .changes-fields {
              color: var(--color-gray-800);
            }
          }

          .diff-list {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .diff-item {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 11px;
              padding: 4px 8px;
              background: rgba(255, 255, 255, 0.6);
              border-radius: 4px;

              .field-name {
                color: var(--color-gray-700);
                font-weight: 500;
                min-width: 60px;
              }

              .diff-arrow {
                color: var(--color-gray-500);
              }

              .field-value {
                color: var(--color-gray-800);
                word-break: break-all;
                flex: 1;
              }
            }
          }
        }
      }

      .truncated-hint {
        margin-top: 8px;
        color: var(--color-gray-500);
        font-size: 12px;
      }
    }

    .tool-error-message {
      padding: 8px 12px;
      background-color: var(--color-danger-transparent);
      border-radius: 6px;
      font-size: 13px;
      color: var(--color-danger);
      line-height: 1.4;
    }
  }
}
/* 错误消息样式 */
.error-content {
  .error-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: var(--color-danger);
    font-weight: 500;

    i {
      font-size: 16px;
    }
  }

  .error-message {
    line-height: 1.5;
    color: var(--color-danger);
  }
}
</style>

Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' 获取当前脚本所在目录
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)

' 获取桌面路径
desktopPath = WshShell.SpecialFolders("Desktop")

' 创建快捷方式
Set shortcut = WshShell.CreateShortcut(desktopPath & "\普通话学习工具.lnk")
shortcut.TargetPath = scriptDir & "\run_json_viewer.bat"
shortcut.WorkingDirectory = scriptDir
shortcut.Description = "普通话学习工具 - 显示随机词汇例句"
shortcut.Save

WScript.Echo "桌面快捷方式创建成功！"
WScript.Echo "位置: " & desktopPath & "\普通话学习工具.lnk"
WScript.Echo "目标: " & scriptDir & "\run_json_viewer.bat"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件内容悬浮弹窗显示器
用于显示普通话学习数据的悬浮窗口
简化版UI界面 - 只显示随机词语
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import random
import glob
from pathlib import Path


class JsonViewerWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.is_collapsed = False
        self.expanded_height = 350
        self.collapsed_height = 30
        self.collapsed_width = 80
        self.expanded_width = 350
        self.json_files = []  # 存储所有JSON文件路径
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.scan_json_files()
        self.show_random_word()

    def setup_window(self):
        """设置窗口属性"""
        self.root.title("普通话学习")
        self.root.geometry(f"{self.expanded_width}x{self.expanded_height}")
        self.root.minsize(80, 30)
        self.root.maxsize(500, 500)

        # 移除窗口装饰（最小化、最大化按钮）
        self.root.overrideredirect(True)

        # 现代化背景色 - 深色主题
        self.root.configure(bg='#1e1e1e')

        # 设置窗口始终在最前面
        self.root.attributes('-topmost', True)

        # 添加圆角效果（Windows 11风格）
        try:
            self.root.attributes('-transparentcolor', '#1e1e1e')
        except:
            pass

        # 居中显示窗口
        self.center_window()

        # 添加窗口拖拽功能
        self.setup_drag_functionality()
        
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.expanded_width
        height = self.expanded_height
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_drag_functionality(self):
        """设置窗口拖拽功能"""
        def start_move(event):
            self.x = event.x
            self.y = event.y

        def on_move(event):
            deltax = event.x - self.x
            deltay = event.y - self.y
            x = self.root.winfo_x() + deltax
            y = self.root.winfo_y() + deltay
            self.root.geometry(f"+{x}+{y}")

        # 绑定拖拽事件到标题栏
        self.root.bind('<Button-1>', start_move)
        self.root.bind('<B1-Motion>', on_move)

    def setup_styles(self):
        """设置样式主题"""
        style = ttk.Style()
        style.theme_use('clam')
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架 - 圆角设计
        main_frame = tk.Frame(self.root, bg='#1a1a1a', relief='flat', bd=0)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 创建圆角容器
        self.create_rounded_container(main_frame)

        # 标题栏（可点击收起/展开）- 圆角顶部
        self.title_frame = tk.Frame(self.container_frame, bg='#3d4147', height=40, cursor='hand2')
        self.title_frame.pack(fill=tk.X)
        self.title_frame.pack_propagate(False)

        # 标题内容
        title_content = tk.Frame(self.title_frame, bg='#3d4147')
        title_content.pack(expand=True, fill=tk.BOTH)

        # 现代化图标和标题
        icon_label = tk.Label(title_content, text="✨",
                             font=('Segoe UI Emoji', 14),
                             fg='#ffffff', bg='#3d4147')
        icon_label.pack(side=tk.LEFT, padx=(16, 6), pady=10)

        self.title_label = tk.Label(title_content, text="",
                                   font=('Microsoft YaHei UI', 12, 'normal'),
                                   fg='#ffffff', bg='#3d4147')
        self.title_label.pack(side=tk.LEFT, padx=(0, 8), pady=10)

        # 现代化控制按钮区域
        controls_frame = tk.Frame(title_content, bg='#3d4147')
        controls_frame.pack(side=tk.RIGHT, padx=12, pady=8)

        # 收起/展开按钮 - 圆角按钮
        self.toggle_indicator = self.create_icon_button(controls_frame, "⌄",
                                                       self.toggle_content, '#3d4147')
        self.toggle_indicator.pack(side=tk.RIGHT, padx=2)

        # 关闭按钮 - 圆角按钮
        close_btn = self.create_icon_button(controls_frame, "✕",
                                           lambda: self.root.quit(), '#3d4147')
        close_btn.pack(side=tk.RIGHT, padx=2)

        # 绑定标题栏点击事件
        self.title_frame.bind('<Button-1>', self.toggle_content)
        title_content.bind('<Button-1>', self.toggle_content)
        self.title_label.bind('<Button-1>', self.toggle_content)
        icon_label.bind('<Button-1>', self.toggle_content)

        # 内容区域 - 现代化卡片设计，减小间隙
        self.content_frame = tk.Frame(self.container_frame, bg='#2a2d31')
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=4, pady=(0, 4))

        # 例句显示区域
        self.create_sentence_display()

        # 控制按钮
        self.create_control_buttons()
        
    def create_rounded_container(self, parent):
        """创建圆角容器效果"""
        # 主容器
        self.container_frame = tk.Frame(parent, bg='#2a2d31', relief='flat', bd=0)
        self.container_frame.pack(fill=tk.BOTH, expand=True)

    def create_icon_button(self, parent, text, command, bg_color):
        """创建圆角图标按钮"""
        btn_frame = tk.Frame(parent, bg=bg_color, width=24, height=24)
        btn_frame.pack_propagate(False)

        btn = tk.Label(btn_frame, text=text,
                      font=('Segoe UI Symbol', 10),
                      fg='#cccccc', bg=bg_color,
                      cursor='hand2')
        btn.pack(expand=True)

        # 添加点击和悬停效果
        self.add_button_effects(btn, btn_frame, command)

        return btn_frame

    def add_button_effects(self, label, frame, command):
        """添加按钮点击和悬停效果"""
        original_bg = frame.cget('bg')

        def on_enter(e):
            frame.config(bg='#4a5568')
            label.config(bg='#4a5568', fg='#ffffff')

        def on_leave(e):
            frame.config(bg=original_bg)
            label.config(bg=original_bg, fg='#cccccc')

        def on_click(e):
            # 点击效果
            frame.config(bg='#2d3748')
            label.config(bg='#2d3748', fg='#ffffff')
            frame.after(100, lambda: on_leave(None))
            if command:
                command()

        label.bind("<Enter>", on_enter)
        label.bind("<Leave>", on_leave)
        label.bind("<Button-1>", on_click)
        frame.bind("<Enter>", on_enter)
        frame.bind("<Leave>", on_leave)
        frame.bind("<Button-1>", on_click)

    def toggle_content(self, event=None):
        """切换内容显示/隐藏，带过渡效果"""
        if self.is_collapsed:
            # 展开 - 添加过渡动画效果
            self.expand_with_animation()
        else:
            # 收起为小按钮 - 现代化最小化
            self.collapse_with_animation()

    def expand_with_animation(self):
        """展开动画效果"""
        # 更新按钮图标
        for child in self.toggle_indicator.winfo_children():
            if isinstance(child, tk.Label):
                child.config(text="⌄")
        self.title_label.config(text="")

        # 先设置透明度为0.3，然后逐渐增加
        self.root.attributes('-alpha', 0.3)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=16, pady=(0, 16))

        # 分步骤调整窗口大小和透明度
        steps = 8
        start_height = self.collapsed_height
        target_height = self.expanded_height
        height_step = (target_height - start_height) // steps

        def animate_step(step):
            if step <= steps:
                # 计算当前高度和透明度
                current_height = start_height + (height_step * step)
                alpha = 0.3 + (0.7 * step / steps)  # 从0.3到1.0

                self.root.geometry(f"{self.expanded_width}x{current_height}")
                self.root.attributes('-alpha', alpha)

                # 继续下一步动画
                self.root.after(25, lambda: animate_step(step + 1))
            else:
                # 动画完成，确保最终状态
                self.root.geometry(f"{self.expanded_width}x{self.expanded_height}")
                self.root.attributes('-alpha', 1.0)
                self.is_collapsed = False

        animate_step(1)

    def collapse_with_animation(self):
        """收起动画效果"""
        # 更新按钮图标
        for child in self.toggle_indicator.winfo_children():
            if isinstance(child, tk.Label):
                child.config(text="⌃")
        self.title_label.config(text="")

        # 快速收起
        self.content_frame.pack_forget()
        self.root.geometry(f"{self.collapsed_width}x{self.collapsed_height}")
        self.root.attributes('-alpha', 0.85)
        self.is_collapsed = True

    def create_sentence_display(self):
        """创建例句显示区域"""
        # 直接使用内容区域，移除边框和多余间隙
        # 创建无滚动条的文本区域，固定高度
        self.sentences_text = tk.Text(self.content_frame,
                                     wrap=tk.WORD,
                                     font=('Microsoft YaHei UI', 11),  # 调小字体
                                     bg='#2a2d31',  # 与背景一致，无边框效果
                                     fg='#dcddde',  # 高对比度文字
                                     relief='flat',
                                     bd=0,
                                     height=7,  # 增加高度，充分利用空间
                                     selectbackground='#5865f2',  # Discord蓝色
                                     selectforeground='#ffffff',
                                     insertbackground='#5865f2',  # 光标颜色
                                     highlightthickness=0,
                                     padx=8,  # 减小内边距
                                     pady=8,
                                     spacing1=3,  # 调小行间距
                                     spacing2=1,
                                     spacing3=3)
        self.sentences_text.pack(fill=tk.X, pady=(8, 0))  # 减小上边距

        # 禁用滚动条
        self.sentences_text.configure(state='normal')
        
    def create_control_buttons(self):
        """创建控制按钮"""
        button_frame = tk.Frame(self.content_frame, bg='#2a2d31')
        button_frame.pack(fill=tk.X, pady=(12, 8))  # 减小间距

        # 现代化按钮容器 - 居中显示
        btn_container = tk.Frame(button_frame, bg='#2a2d31')
        btn_container.pack(expand=True)

        # 换一个按钮 - 低调设计，更小
        next_btn_frame = tk.Frame(btn_container, bg='#4a4a4a', relief='flat', bd=0)
        next_btn_frame.pack(side=tk.LEFT, padx=(0, 16))

        next_btn = tk.Label(next_btn_frame, text="换一个",  # 移除emoji，更简洁
                           font=('Microsoft YaHei UI', 8),  # 更小字体
                           bg='#4a4a4a', fg='#cccccc',  # 低调颜色
                           cursor='hand2',
                           padx=12, pady=4)  # 更小内边距
        next_btn.pack()

        # 添加按钮效果 - 低调颜色
        self.add_premium_button_effects(next_btn, next_btn_frame, self.show_random_word,
                                       '#3a3a3a', '#4a4a4a', '#5a5a5a')

        # 置顶切换按钮 - 现代化开关样式，更小
        self.topmost_var = tk.BooleanVar(value=True)
        topmost_container = tk.Frame(btn_container, bg='#2a2d31')
        topmost_container.pack(side=tk.RIGHT)

        topmost_label = tk.Label(topmost_container, text="置顶",
                                font=('Microsoft YaHei UI', 8),  # 更小字体
                                bg='#2a2d31', fg='#888888')  # 更低调颜色
        topmost_label.pack(side=tk.LEFT, padx=(0, 8))

        # 创建现代化开关，更小
        self.create_toggle_switch(topmost_container)
        
    def add_premium_button_effects(self, label, frame, command, click_color, normal_color, hover_color):
        """添加高级按钮效果"""
        def on_enter(e):
            frame.config(bg=hover_color)
            label.config(bg=hover_color)

        def on_leave(e):
            frame.config(bg=normal_color)
            label.config(bg=normal_color)

        def on_click(e):
            print("按钮被点击了！")  # 添加调试信息
            # 点击动画效果
            frame.config(bg=click_color)
            label.config(bg=click_color)
            frame.after(120, lambda: on_leave(None))
            if command:
                print("执行命令...")  # 添加调试信息
                try:
                    frame.after(60, command)
                except Exception as ex:
                    print(f"执行命令时出错: {ex}")  # 添加错误调试

        label.bind("<Enter>", on_enter)
        label.bind("<Leave>", on_leave)
        label.bind("<Button-1>", on_click)
        frame.bind("<Enter>", on_enter)
        frame.bind("<Leave>", on_leave)
        frame.bind("<Button-1>", on_click)

    def create_toggle_switch(self, parent):
        """创建现代化开关，更小尺寸"""
        switch_frame = tk.Frame(parent, bg='#4f545c', width=32, height=18, relief='flat', bd=0)  # 更小尺寸
        switch_frame.pack_propagate(False)
        switch_frame.pack(side=tk.RIGHT)

        # 开关滑块，更小
        self.switch_slider = tk.Frame(switch_frame, bg='#ffffff', width=14, height=14, relief='flat', bd=0)
        self.switch_slider.place(x=16, y=2)  # 初始位置（开启状态）

        # 绑定点击事件
        def toggle_switch():
            current = self.topmost_var.get()
            self.topmost_var.set(not current)
            self.toggle_topmost()
            self.update_switch_appearance()

        switch_frame.bind("<Button-1>", lambda e: toggle_switch())
        self.switch_slider.bind("<Button-1>", lambda e: toggle_switch())

        # 初始化开关外观
        self.update_switch_appearance()

    def update_switch_appearance(self):
        """更新开关外观"""
        if self.topmost_var.get():
            # 开启状态 - 低调颜色
            self.switch_slider.master.config(bg='#666666')  # 更低调的颜色
            self.switch_slider.place(x=16, y=2)
        else:
            # 关闭状态
            self.switch_slider.master.config(bg='#4f545c')
            self.switch_slider.place(x=2, y=2)
        
    def toggle_topmost(self):
        """切换窗口置顶状态"""
        self.root.attributes('-topmost', self.topmost_var.get())
        
    def scan_json_files(self):
        """扫描putonghuaData目录下的所有JSON文件"""
        try:
            # 获取脚本所在目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            print(f"脚本目录: {script_dir}")  # 添加调试信息

            # 在脚本所在目录下查找JSON文件
            json_pattern = os.path.join(script_dir, "*.json")
            self.json_files = glob.glob(json_pattern)

            if not self.json_files:
                # 如果在脚本目录没找到，尝试在当前工作目录查找
                current_dir_pattern = "*.json"
                self.json_files = glob.glob(current_dir_pattern)

            if not self.json_files:
                self.show_error("未找到任何JSON文件")
                return

            print(f"找到 {len(self.json_files)} 个JSON文件: {self.json_files}")

        except Exception as e:
            print(f"扫描JSON文件时出错: {e}")  # 添加调试信息
            self.show_error(f"扫描JSON文件时出错: {e}")

    def load_random_json_data(self):
        """从随机JSON文件中加载数据"""
        if not self.json_files:
            self.show_error("没有可用的JSON文件")
            return None

        # 随机选择一个JSON文件
        random_file = random.choice(self.json_files)

        try:
            with open(random_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 验证数据结构
            if not isinstance(data, dict):
                raise ValueError("JSON文件格式错误：根对象不是字典")

            if 'words' not in data:
                raise ValueError("JSON文件格式错误：缺少'words'字段")

            if not isinstance(data['words'], list):
                raise ValueError("JSON文件格式错误：'words'字段不是列表")

            print(f"成功加载文件: {random_file}")
            return data

        except json.JSONDecodeError as e:
            self.show_error(f"JSON解析错误 ({random_file}): {e}")
            return None
        except Exception as e:
            self.show_error(f"加载文件时出错 ({random_file}): {e}")
            return None

    def show_random_word(self):
        """从随机JSON文件中显示随机词汇的例句"""
        print("show_random_word 方法被调用了！")  # 添加调试信息

        # 重新扫描JSON文件（以防文件有变化）
        self.scan_json_files()

        # 加载随机JSON文件数据
        data = self.load_random_json_data()
        if not data:
            print("没有加载到数据")  # 添加调试信息
            return

        words = data.get('words', [])
        if not words:
            print("没有找到词汇数据")  # 添加调试信息
            self.show_error("选中的文件中没有找到词汇数据")
            return

        print(f"找到 {len(words)} 个词汇")  # 添加调试信息

        # 随机选择一个词汇
        try:
            random_word = random.choice(words)
            print(f"选中词汇: {random_word.get('text', '未知')}")  # 添加调试信息

            # 验证词汇数据结构
            if not isinstance(random_word, dict):
                raise ValueError("词汇数据格式错误：不是字典对象")

            sentences = random_word.get('sentences', [])
            if not isinstance(sentences, list):
                raise ValueError("例句数据格式错误：不是列表")

            print(f"找到 {len(sentences)} 个例句")  # 添加调试信息

            # 直接显示例句，不显示词汇本身
            self.display_sentences(sentences)
            print("例句显示完成")  # 添加调试信息

        except Exception as e:
            print(f"处理词汇数据时出错: {e}")  # 添加调试信息
            self.show_error(f"处理词汇数据时出错: {e}")
        
    def display_sentences(self, sentences):
        """显示例句，精美现代化样式，移除序号"""
        # 先启用文本框以便更新内容
        self.sentences_text.configure(state='normal')
        self.sentences_text.delete(1.0, tk.END)

        if sentences:
            try:
                for i, sentence in enumerate(sentences, 1):
                    # 安全获取文本和拼音，处理可能的数据格式问题
                    if isinstance(sentence, dict):
                        text = sentence.get('text', '')
                        pinyin = sentence.get('pinyin', '')
                    else:
                        # 如果sentence不是字典，尝试转换为字符串显示
                        text = str(sentence)
                        pinyin = ''

                    # 简洁显示格式 - 移除绿色小点
                    self.sentences_text.insert(tk.END, f"{text}\n", 'sentence')
                    if pinyin:
                        self.sentences_text.insert(tk.END, f"{pinyin}\n\n", 'pinyin')
                    else:
                        self.sentences_text.insert(tk.END, "\n", 'spacing')

            except Exception as e:
                self.sentences_text.insert(tk.END, f"显示例句时出错: {e}", 'error')
        else:
            self.sentences_text.insert(tk.END, "✨ 暂无例句数据", 'empty')

        # 简洁现代化文本样式配置
        self.sentences_text.tag_configure('sentence',
                                         foreground='#ffffff',  # 纯白文字
                                         font=('Microsoft YaHei UI', 11, 'normal'),  # 调小字体
                                         spacing1=2, spacing3=3)
        self.sentences_text.tag_configure('pinyin',
                                         foreground='#99aab5',  # 优雅灰色拼音
                                         font=('Consolas', 9, 'italic'))  # 调小拼音字体
        self.sentences_text.tag_configure('error',
                                         foreground='#f04747',  # Discord红色
                                         font=('Microsoft YaHei UI', 11))
        self.sentences_text.tag_configure('empty',
                                         foreground='#72767d',  # 空状态灰色
                                         font=('Microsoft YaHei UI', 11, 'italic'))
        self.sentences_text.tag_configure('spacing',
                                         font=('Microsoft YaHei UI', 4))

        # 禁用编辑
        self.sentences_text.configure(state='disabled')
        
    def show_error(self, message):
        """显示错误信息"""
        messagebox.showerror("错误", message)

        # 先启用文本框以便更新内容
        self.sentences_text.configure(state='normal')
        self.sentences_text.delete(1.0, tk.END)
        self.sentences_text.insert(tk.END, f"错误: {message}")
        # 禁用编辑
        self.sentences_text.configure(state='disabled')
        
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    app = JsonViewerWindow()
    app.run()


if __name__ == "__main__":
    main()

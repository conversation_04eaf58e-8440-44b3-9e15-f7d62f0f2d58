'use strict'

/**
 * Clear `triggerQuestion` arrays in all JSON files under putonghuaData.
 * - If the field exists, set it to []
 * - Keep other fields and their order untouched
 */

const fs = require('fs')
const path = require('path')

const ROOT = process.cwd()
const DATA_DIR = path.join(ROOT, 'putonghuaData')

function processFile(filePath) {
  const raw = fs.readFileSync(filePath, 'utf8')
  let data
  try {
    data = JSON.parse(raw)
  } catch (e) {
    console.error(`[skip] Invalid JSON: ${filePath}`)
    return { updated: false }
  }

  if (!Object.prototype.hasOwnProperty.call(data, 'triggerQuestion')) {
    return { updated: false }
  }

  // Only change if not already an empty array
  const current = data.triggerQuestion
  const isAlreadyEmptyArray = Array.isArray(current) && current.length === 0
  if (isAlreadyEmptyArray) {
    return { updated: false }
  }

  // Set to empty array regardless of previous type/value
  data.triggerQuestion = []

  const json = JSON.stringify(data, null, 2) + '\n'
  fs.writeFileSync(filePath, json, 'utf8')
  return { updated: true }
}

function main() {
  if (!fs.existsSync(DATA_DIR)) {
    console.error(`Data directory not found: ${DATA_DIR}`)
    process.exit(1)
  }

  const files = fs.readdirSync(DATA_DIR)
    .filter(f => f.endsWith('.json'))
    .map(f => path.join(DATA_DIR, f))

  let updated = 0
  for (const file of files) {
    const res = processFile(file)
    if (res.updated) {
      updated++
      console.log(`[ok] ${path.basename(file)} -> triggerQuestion cleared`) // eslint-disable-line no-console
    } else {
      console.log(`[skip] ${path.basename(file)}`) // eslint-disable-line no-console
    }
  }

  console.log(`Done. Updated ${updated}/${files.length} files.`)
}

if (require.main === module) {
  main()
}



<template>
  <div class="z-confirm-modal" v-if="visible">
    <div class="z-confirm-overlay" @click="handleCancel"></div>
    <div class="z-confirm-content" :class="{ danger: type === 'danger' }">
      <div class="z-confirm-icon" v-if="showIcon">
        <i class="fas" :class="iconClass"></i>
      </div>
      <div class="z-confirm-title">{{ title }}</div>
      <div class="z-confirm-message">{{ message }}</div>
      <div class="z-confirm-actions">
        <button class="z-confirm-cancel" @click="handleCancel">
          <span>{{ cancelText }}</span>
        </button>
        <button class="z-confirm-confirm" @click="handleConfirm">
          <span>{{ confirmText }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 定义组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '确认',
  },
  message: {
    type: String,
    default: '确定要执行此操作吗？',
  },
  confirmText: {
    type: String,
    default: '确定',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  type: {
    type: String,
    default: 'primary',
    validator: (value: string) => ['primary', 'danger', 'warning', 'success'].includes(value),
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
})

// 定义事件
const emit = defineEmits(['confirm', 'cancel', 'update:visible'])

// 计算图标类名
const iconClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'fa-exclamation-triangle'
    case 'warning':
      return 'fa-exclamation-circle'
    case 'success':
      return 'fa-check-circle'
    default:
      return 'fa-question-circle'
  }
})

// 处理确认按钮点击
const handleConfirm = () => {
  emit('confirm')
  emit('update:visible', false)
}

// 处理取消按钮点击
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
.z-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.z-confirm-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
  z-index: 1001;
}

.z-confirm-content {
  position: relative;
  z-index: 1002;
  background-color: var(--color-white);
  border-radius: var(--rounded-xl);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 380px;
  min-width: 280px;
  padding: 32px 24px 24px;
  text-align: center;
  animation: modalFadeIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

  // 响应式设计
  @media (max-width: 480px) {
    margin: 0 16px;
    padding: 28px 20px 20px;
    max-width: calc(100vw - 32px);
  }

  &.danger {
    .z-confirm-icon {
      color: var(--color-danger);
    }

    .z-confirm-confirm {
      background-color: var(--color-danger);
      border-color: var(--color-danger);

      &:hover {
        background-color: var(--color-danger);
        opacity: 0.9;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  &.warning {
    .z-confirm-icon {
      color: var(--color-warning);
    }

    .z-confirm-confirm {
      background-color: var(--color-warning);
      border-color: var(--color-warning);

      &:hover {
        background-color: var(--color-warning);
        opacity: 0.9;
        transform: translateY(-1px);
      }
    }
  }

  &.success {
    .z-confirm-icon {
      color: var(--color-success);
    }

    .z-confirm-confirm {
      background-color: var(--color-success);
      border-color: var(--color-success);

      &:hover {
        background-color: var(--color-success);
        opacity: 0.9;
        transform: translateY(-1px);
      }
    }
  }
}

.z-confirm-icon {
  font-size: 52px;
  color: var(--color-primary);
  margin-bottom: 20px;
  line-height: 1;

  // 添加图标动画效果
  animation: iconPulse 0.6s ease-out;
}

.z-confirm-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 16px;
  line-height: 1.3;

  @media (max-width: 480px) {
    font-size: 18px;
    margin-bottom: 14px;
  }
}

.z-confirm-message {
  font-size: 15px;
  color: var(--color-text-secondary);
  margin-bottom: 32px;
  line-height: 1.6;
  max-width: 280px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 480px) {
    font-size: 14px;
    margin-bottom: 28px;
  }
}

.z-confirm-actions {
  display: flex;
  gap: 16px;
  justify-content: center;

  @media (max-width: 480px) {
    gap: 12px;
  }

  button {
    flex: 1;
    min-width: 88px;
    padding: 12px 20px;
    border-radius: var(--rounded-lg);
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;

    @media (max-width: 480px) {
      padding: 11px 16px;
      font-size: 14px;
    }

    // 添加按钮点击波纹效果
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transition: width 0.3s, height 0.3s, top 0.3s, left 0.3s;
      transform: translate(-50%, -50%);
      z-index: 0;
    }

    &:active::before {
      width: 200px;
      height: 200px;
    }

    span {
      position: relative;
      z-index: 1;
    }
  }
}

.z-confirm-cancel {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border-color: var(--color-gray-200);

  &:hover {
    background-color: var(--color-gray-200);
    border-color: var(--color-gray-300);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.z-confirm-confirm {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
  font-weight: 600;

  &:hover {
    background-color: var(--color-primary-dark, #2563eb);
    border-color: var(--color-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes iconPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>

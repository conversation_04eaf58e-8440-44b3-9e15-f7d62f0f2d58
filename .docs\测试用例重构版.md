# AI 云函数 TodoTool 测试用例（重构版）

## 概述

本文档基于 `@todo/` 最新实现与 `ai/modules/config.js` 中 FUNCTION_TOOLS 定义重构测试用例，覆盖任务、清单、标签与时间工具的标准与异常场景。全部用例均提供口语化“用户输入”示例（适配语音转文字）。

---

## 测试数据集（更真实）

为提升可用性，提供三套更贴近真实场景的数据集。默认使用数据集 A；如需切换，请将下文用例中的清单/任务/标签名称替换为目标数据集对应名称。

- 数据集 A（默认：日常与工作并行）
  - 清单：
    - 旅游计划
    - 工作清单
    - 学习计划
  - 任务：
    - 收拾行李箱（旅游计划）
    - 完成清单报告（工作清单）
    - 复习数据结构（学习计划）
    - 明天吃牛肉（无清单或临时）
  - 标签：重要、出差

- 数据集 B（家庭与健康场景）
  - 清单：
    - 家庭采购
    - 健身计划
    - 孩子学习
  - 任务：
    - 本周牛奶鸡蛋补货（家庭采购）
    - 晚上跑步 5 公里（健身计划）
    - 数学口算练习（孩子学习）
    - 预约牙医（无清单或临时）
  - 标签：家庭、健康

- 数据集 C（搬家与项目发布）
  - 清单：
    - 搬家清单
    - 发布计划
    - 阅读清单
  - 任务：
    - 联系搬家公司（搬家清单）
    - 发布前自测（发布计划）
    - 《人月神话》读书笔记（阅读清单）
    - 买收纳箱（无清单或临时）
  - 标签：紧急、文档

运行示例：
```javascript
const todoTool = new TodoTool()
const result = await todoTool.execute(toolName, parameters)
console.log('测试结果：', result)
```

---

## 一、基础数据创建

### 1.1 createProject - 创建清单

- [ ] 创建旅游清单
  - 工具：`createProject`
  - 参数：`{ "name": "旅游计划" }`
  - 用户输入："新建一个旅游计划的清单"
  - 预期：成功创建清单，返回清单基础信息（id/name/color/...）

- [ ] 创建工作清单（带颜色）
  - 工具：`createProject`
  - 参数：`{ "name": "工作清单", "color": "#ff6b6b" }`
  - 用户输入："建个工作清单，颜色用#ff6b6b"
  - 预期：成功创建清单，color 为指定值

- [ ] 创建学习清单
  - 工具：`createProject`
  - 参数：`{ "name": "学习计划" }`
  - 用户输入："再建一个学习计划的清单"
  - 预期：成功创建清单

错误处理：
- [ ] 缺少必填参数 name
  - 工具：`createProject`
  - 参数：`{ "color": "#3498db" }`
  - 用户输入："先随便建个蓝色清单，不用名字"
  - 预期：返回错误，提示缺少清单名称

### 1.2 createTask - 创建任务

- [ ] 创建旅游任务：收拾行李箱
  - 工具：`createTask`
  - 参数：`{ "title": "收拾行李箱", "projectName": "旅游计划", "priority": 3 }`
  - 用户输入："在旅游计划里加个收拾行李箱，优先级三"
  - 预期：成功创建，归属旅游计划

- [ ] 创建工作任务：完成清单报告（带截止日期）
  - 工具：`createTask`
  - 参数：`{ "title": "完成清单报告", "content": "包含数据分析与结论", "projectName": "工作清单", "priority": 5, "dueDate": "2024-12-31 23:59:59" }`
  - 用户输入："工作清单加个完成清单报告，优先级五，截止到二零二四年十二月三十一号晚上"
  - 预期：成功创建，高优先级、截止时间正确

- [ ] 创建学习任务：复习数据结构
  - 工具：`createTask`
  - 参数：`{ "title": "复习数据结构", "projectName": "学习计划", "priority": 1 }`
  - 用户输入："学习计划里加一个复习数据结构，优先级一"
  - 预期：成功创建

- [ ] 创建简单任务：明天吃牛肉
  - 工具：`createTask`
  - 参数：`{ "title": "明天吃牛肉" }`
  - 用户输入："记一下，明天吃牛肉"
  - 预期：成功创建最简任务

- [ ] 创建任务（设置 kind、全天与提醒）
  - 工具：`createTask`
  - 参数：`{ "title": "出差准备清单", "projectName": "工作清单", "kind": "TEXT", "isAllDay": true, "reminder": "-1D" }`
  - 用户输入："工作清单里加个出差准备清单，全天的，提醒提前一天"
  - 预期：成功创建，字段映射正确

错误处理：
- [ ] 缺少必填参数 title
  - 工具：`createTask`
  - 参数：`{ "content": "没有标题的任务" }`
  - 用户输入："帮我建个任务，内容写要准备材料"
  - 预期：返回错误，提示缺少标题

---

## 二、数据查询验证

### 2.1 getProjects - 获取清单列表

- [ ] 获取所有清单
  - 工具：`getProjects`
  - 参数：`{}`
  - 用户输入："把我现在所有清单都列出来"
  - 预期：返回包含「旅游计划」「工作清单」「学习计划」的清单列表

说明：该工具当前不支持关键词或是否关闭等筛选参数，直接返回列表。

### 2.2 getTasks - 获取任务列表

基础功能：
- [ ] 获取所有任务
  - 工具：`getTasks`
  - 参数：`{}`
  - 用户输入："最近的任务都给我看看"
  - 预期：返回包含已创建任务的列表

- [ ] 查看旅游计划任务
  - 工具：`getTasks`
  - 参数：`{ "projectName": "旅游计划" }`
  - 用户输入："只看旅游计划下面的任务"
  - 预期：返回「收拾行李箱」任务

- [ ] 查看工作清单任务
  - 工具：`getTasks`
  - 参数：`{ "projectName": "工作清单" }`
  - 用户输入："把工作清单的任务列出来"
  - 预期：返回「完成清单报告」任务

- [ ] 查看高优先级任务（priority=5）
  - 工具：`getTasks`
  - 参数：`{ "priority": 5 }`
  - 用户输入："优先级五的任务给我看一下"
  - 预期：返回「完成清单报告」

- [ ] 查看未完成任务
  - 工具：`getTasks`
  - 参数：`{ "completed": false }`
  - 用户输入："只要还没完成的任务"
  - 预期：返回所有未完成任务

- [ ] 关键词搜索（包含“牛肉”）
  - 工具：`getTasks`
  - 参数：`{ "keyword": "牛肉" }`
  - 用户输入："搜一下带牛肉这个词的任务"
  - 预期：返回「明天吃牛肉」任务

时间与组合筛选：
- [ ] 今天的任务（默认只显示未完成）
  - 工具：`getTasks`
  - 参数：`{ "mode": "today" }`
  - 用户输入："今天的任务都有什么"
  - 预期：仅返回今天范围内的未完成任务

- [ ] 最近7天任务
  - 工具：`getTasks`
  - 参数：`{ "mode": "recent_7_days" }`
  - 用户输入："最近七天的任务帮我筛一下"
  - 预期：返回最近 7 天内的任务

- [ ] 工作清单中未完成的高优先级任务
  - 工具：`getTasks`
  - 参数：`{ "projectName": "工作清单", "completed": false, "priority": 5 }`
  - 用户输入："工作清单里没完成而且优先级五的有哪些"
  - 预期：返回「完成清单报告」（如未完成）

分页：
- [ ] 限制返回数量与偏移
  - 工具：`getTasks`
  - 参数：`{ "limit": 2, "offset": 1 }`
  - 用户输入："任务分页看一下，两条，从第二条开始"
  - 预期：正确返回分页后的子集

---

## 三、数据更新操作

### 3.1 updateProject - 更新清单（对齐最新签名）

- [ ] 更新旅游清单名称
  - 工具：`updateProject`
  - 参数：`{ "projectIdOrName": "旅游计划", "name": "春节旅游计划" }`
  - 用户输入："把旅游计划这个清单改名叫春节旅游计划"
  - 预期：成功更新名称

- [ ] 更新工作清单颜色
  - 工具：`updateProject`
  - 参数：`{ "projectIdOrName": "工作清单", "color": "#2ecc71" }`
  - 用户输入："工作清单的颜色改成绿色，那个#2ecc71"
  - 预期：成功更新颜色

错误处理：
- [ ] 指定不存在的清单
  - 工具：`updateProject`
  - 参数：`{ "projectIdOrName": "不存在的清单", "name": "任意名称" }`
  - 用户输入："把不存在的清单改个名字试试"
  - 预期：返回错误，提示清单不存在

说明：当前不支持关闭/归档字段；仅支持 name 与 color 更新。

### 3.2 updateTask - 更新任务

- [ ] 完成收拾行李箱任务
  - 工具：`updateTask`
  - 参数：`{ "taskId": "task_收拾行李箱", "completed": true }`
  - 用户输入："把收拾行李箱这一条标成完成"
  - 预期：任务状态映射为完成

- [ ] 更新清单报告任务内容
  - 工具：`updateTask`
  - 参数：`{ "taskId": "task_完成清单报告", "content": "包含数据分析、结论和建议" }`
  - 用户输入："把完成清单报告的内容更新一下，加上建议那段"
  - 预期：内容更新成功

- [ ] 提高复习任务优先级
  - 工具：`updateTask`
  - 参数：`{ "taskId": "task_复习数据结构", "priority": 3 }`
  - 用户输入："复习数据结构优先级改成三"
  - 预期：优先级更新成功

- [ ] 将“明天吃牛肉”移到旅游清单
  - 工具：`updateTask`
  - 参数：`{ "taskId": "task_明天吃牛肉", "projectName": "旅游计划" }`
  - 用户输入："把明天吃牛肉移到旅游计划这个清单"
  - 预期：成功移动到对应清单

- [ ] 批量字段更新（标题+优先级+未完成）
  - 工具：`updateTask`
  - 参数：`{ "taskId": "task_完成清单报告", "title": "完成清单报告（终版）", "priority": 5, "completed": false }`
  - 用户输入："把完成清单报告改成终版，优先级五，状态改回未完成"
  - 预期：多个字段同时更新

错误处理：
- [ ] 指定不存在的任务
  - 工具：`updateTask`
  - 参数：`{ "taskId": "task_不存在的任务", "completed": true }`
  - 用户输入："把那个不存在的任务标完成看看"
  - 预期：返回错误，提示任务不存在

---

## 四、数据删除清理

### 4.1 deleteTask - 删除任务

- [ ] 删除已完成的收拾行李箱
  - 工具：`deleteTask`
  - 参数：`{ "taskId": "task_收拾行李箱" }`
  - 用户输入："把收拾行李箱这条删掉"
  - 预期：成功删除

- [ ] 删除不存在的任务
  - 工具：`deleteTask`
  - 参数：`{ "taskId": "task_不存在的任务" }`
  - 用户输入："删一下那个不存在的任务"
  - 预期：返回错误，提示任务不存在

### 4.2 deleteProject - 删除清单（对齐最新签名）

- [ ] 删除学习清单
  - 工具：`deleteProject`
  - 参数：`{ "projectIdOrName": "学习计划" }`
  - 用户输入："把学习计划这个清单删了"
  - 预期：成功删除

- [ ] 删除包含任务的工作清单（可能被业务限制）
  - 工具：`deleteProject`
  - 参数：`{ "projectIdOrName": "工作清单" }`
  - 用户输入："试试把工作清单删掉"
  - 预期：若后端限制，返回错误或提示；否则删除成功

---

## 五、标签工具测试

### 5.1 getTags - 获取标签

- [ ] 获取所有标签
  - 工具：`getTags`
  - 参数：`{}`
  - 用户输入："把所有标签都给我看看"
  - 预期：返回标签数组

### 5.2 createTag - 创建标签

- [ ] 创建“重要”标签（带颜色）
  - 工具：`createTag`
  - 参数：`{ "name": "重要", "color": "#e74c3c" }`
  - 用户输入："新建一个标签，叫重要，颜色红色#e74c3c"
  - 预期：成功创建

- [ ] 缺少名称
  - 工具：`createTag`
  - 参数：`{ "color": "#e74c3c" }`
  - 用户输入："创建一个标签就行，颜色红色，不用名字"
  - 预期：返回错误，提示 name 不能为空

### 5.3 updateTag - 更新标签

- [ ] 更新“重要”标签名称与颜色
  - 工具：`updateTag`
  - 参数：`{ "tagIdOrName": "重要", "name": "重要-紧急", "color": "#c0392b" }`
  - 用户输入："把重要这个标签改名成重要紧急，颜色换成#c0392b"
  - 预期：重命名并更新颜色成功

- [ ] 更新不存在的标签
  - 工具：`updateTag`
  - 参数：`{ "tagIdOrName": "不存在", "color": "#000000" }`
  - 用户输入："把不存在这个标签颜色改成黑色"
  - 预期：返回错误，提示标签不存在

### 5.4 deleteTag - 删除标签

- [ ] 删除“重要-紧急”
  - 工具：`deleteTag`
  - 参数：`{ "tagIdOrName": "重要-紧急" }`
  - 用户输入："把重要紧急这个标签删了"
  - 预期：成功删除

- [ ] 删除不存在的标签
  - 工具：`deleteTag`
  - 参数：`{ "tagIdOrName": "不存在" }`
  - 用户输入："删掉那个不存在的标签"
  - 预期：返回错误，提示标签不存在

### 5.5 renameTag - 重命名

- [ ] 将“出差”重命名为“出差相关”
  - 工具：`renameTag`
  - 参数：`{ "oldName": "出差", "newName": "出差相关" }`
  - 用户输入："把出差这个标签改名成出差相关"
  - 预期：成功重命名

### 5.6 mergeTags - 合并

- [ ] 将“出差相关”合并到“重要”
  - 工具：`mergeTags`
  - 参数：`{ "sourceName": "出差相关", "targetName": "重要" }`
  - 用户输入："把出差相关合并到重要这个标签"
  - 预期：成功合并

---

## 六、时间工具测试

### 6.1 getCurrentTimeInfo - 获取当前时间信息

- [ ] 获取详细时间信息（默认 detailed）
  - 工具：`getCurrentTimeInfo`
  - 参数：`{}`
  - 用户输入："现在的时间详细说一下"
  - 预期：返回详细时间 + 相对时间说明

- [ ] 获取 ISO 格式时间
  - 工具：`getCurrentTimeInfo`
  - 参数：`{ "format": "iso" }`
  - 用户输入："给我 ISO 格式的当前时间"
  - 预期：返回 ISO 8601 格式时间

- [ ] 获取本地（中文）时间
  - 工具：`getCurrentTimeInfo`
  - 参数：`{ "format": "local" }`
  - 用户输入："现在时间用中文说"
  - 预期：返回中文本地化时间

---

## 七、复合操作测试

- [ ] 创建清单 + 创建任务
  - 流程：`createProject` → `createTask`
  - 用户输入："先建个旅游计划清单，然后加个收拾行李箱"
  - 预期：两个操作成功，任务正确归属清单

- [ ] 查询 + 更新
  - 流程：`getTasks`（筛选未完成）→ `updateTask`（标记完成）
  - 用户输入："先看下没完成的，然后把复习数据结构标成完成"
  - 预期：能正确查询并更新任务状态

- [ ] 创建 + 查询 + 删除（临时任务）
  - 流程：`createTask` → `getTasks`（确认创建）→ `deleteTask`
  - 用户输入："先加个临时任务，确认有了就删掉"
  - 预期：流程顺利完成

- [ ] 清单生命周期（对齐最新签名）
  - 流程：`createProject` → `createTask` → `updateProject`（改名或改色） → `deleteProject`
  - 用户输入："建个测试清单，加条测试任务，清单改个颜色，最后把清单删了"
  - 预期：各步骤执行成功，列表/任务随之正确变更

---

## 执行说明与通过标准

执行顺序：
1. 基础数据创建
2. 数据查询验证
3. 数据更新操作
4. 数据删除清理
5. 时间工具测试
6. 复合操作测试

数据一致性要求：
- 默认使用数据集 A（如切换到 B/C，请全局替换名称以保持一致）
- 查询用例基于已创建数据
- 更新/删除使用真实存在的 ID 或名称

通过标准：
- 单工具测试通过率 100%
- 复合操作通过率 ≥ 95%
- 数据一致性无异常
- 错误处理返回语义与提示准确

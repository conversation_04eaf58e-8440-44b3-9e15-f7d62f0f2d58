# AI 助手：工具执行“详情”展示需求

## 背景

当前 AI 助手在调用后端工具（Function Calling）后，会通过 SSE 向前端推送工具执行结果，用于在消息气泡中展示“执行成功/失败”、“数据条数”和“耗时”等摘要信息。但对于增删改类操作（非查询）缺少可读的“操作详情”，无法直观反馈本次实际改动的对象与字段变化，影响可用性与可追溯性。

本需求在不破坏现有行为的前提下，为非查询类（create/update/delete）工具执行结果增加“详情”数据，并在前端消息气泡中结构化展示。

## 需求

 - 后端在工具执行完成时（`TOOL_EXECUTION_COMPLETE`）除现有摘要外，新增返回“详情”数据：仅对非查询操作返回详情；查询操作不返回详情（严禁回传任务列表等大体量数据，以免导致 SSE 过载报错）。
- 前端 `src/pages/aiAssistant/components/l-message-bubble.vue` 在渲染 `type === 'tool_result'` 时，若存在详情则展开展示（默认折叠，支持点击展开/收起），包含操作类型、影响条数、对象 ID 列表、字段变更摘要等。
- 兼容性：无“详情”字段时，沿用现有摘要展示，不影响旧版本。

## 数据结构规范（SSE）

事件类型：`tool_execution_complete`

现有结构（保持不变）：
```json
{
  "type": "tool_execution_complete",
  "sessionId": "string",
  "data": {
    "toolName": "string",
    "result": {
      "message": "string",          // 摘要，可能含数据条数
      "executionTime": 123,           // 毫秒
      "dataCount": 0,                 // 数据条数（查询/汇总场景）
      "errCode": 0
      // + 本需求新增：details（见下）
    },
    "success": true,
    "toolCallId": "string"
  },
  "timestamp": "ISO 8601"
}
```

新增字段：`result.details`（对象，查询操作禁止返回）

通用字段（所有 operationType 通用）：
```json
{
  "operationType": "query|create|update|delete",
  "summary": "string",             // 友好可读的操作摘要
  "rowsAffected": 0,                // 受影响行数/对象数（create=新增数, update=更新数, delete=删除数）
  "affectedIds": ["string"],       // 受影响对象ID（如有）
  "truncated": false                // 当详情被截断时为 true
}
```

各类操作的扩展字段（仅非查询类返回，且只返回“改动字段”/最小必要字段）：

- create：
```json
{
  "operationType": "create",
  "createdIds": ["string"],
  "createdCount": 0,
  // 严格最小化，仅返回必要字段（例如：标题与时间）
  "preview": [ { "_id": "string", "title": "...", "startDate": "YYYY-MM-DDTHH:mm:ssZ", "dueDate": "YYYY-MM-DDTHH:mm:ssZ" } ]
}
```

- update：
```json
{
  "operationType": "update",
  "updatedIds": ["string"],
  "updatedCount": 0,
  // 仅返回“发生改动”的字段（最小必要集），不回传完整对象
  "changes": [
    {
      "_id": "string",
      "changedFields": ["dueDate"],
      "diff": {
        "dueDate": { "before": "2025-05-23T15:00:00Z", "after": "2025-05-24T15:00:00Z" }
      },
      // 可选最小上下文：标题，便于前端可读展示
      "title": "任务标题"
    }
  ]
}
```

- delete：
```json
{
  "operationType": "delete",
  "deletedIds": ["string"],
  "deletedCount": 0,
  // 可选最小上下文：标题（如可得），不包含其它无关字段
  "titles": ["任务标题1", "任务标题2"]
}
```

// 查询（query）：不返回 details，不回传列表预览/大体量数据

限制与约束：
- 为防止 SSE 负载过大：
  - `affectedIds`、`preview`、`changes` 默认最多各 20 条（可在后端以常量限制）。
  - `diff` 仅包含变化字段，不返回完整对象；字符串字段可按长度截断（如 120 字符）。
  - 当触发截断时将 `truncated` 置为 `true`。
  - 查询类严禁回传 `details`、任务列表或预览，仅允许使用摘要 `message` 与计数类字段（如 `dataCount`）。

## 后端改动点（uniCloud-aliyun/cloudfunctions/ai/index.obj.js）

位置：`executeToolCall` 完成调用并生成 `summarizedResult` 处。

新增逻辑（示意）：
```js
// 伪代码：从工具原始返回 result 中提取 operationType 与详情数据
const operationType = inferOperationTypeFromTool(toolName, result) // create|update|delete|query
const details = buildDetails(operationType, result) // 组装为上面的规范

const summarizedResult = {
  message: summarizedMessage,
  executionTime: result?.executionTime || null,
  dataCount: dataCount,
  errCode: result?.errCode ?? 0,
  details, // ← 新增字段
}

await sseChannel.write(createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_COMPLETE, sessionId, {
  toolName: toolName,
  result: summarizedResult,
  success: true,
  toolCallId: toolCall.id,
}))
```

若工具本身已给出结构化返回（如 `result.operationType`、`result.affectedIds` 等），可直接映射；否则按工具名/路由推断 `operationType`。查询类不构建 `details`；非查询类仅构建“改动字段”的最小必要集（例如：创建仅返回标题与时间；更新仅返回发生改动的字段及 title；删除仅返回 id 与可选 title）。

错误事件 `TOOL_EXECUTION_ERROR` 无需改动结构（继续返回 `toolName` 与 `error`）。

## 前端改动点

组件：`src/pages/aiAssistant/components/l-message-bubble.vue`

现状：已渲染成功/失败摘要与耗时。新增：当 `toolResult?.details` 存在时，渲染“详情”区块（默认折叠，可点击展开）。

展示规则：
- 头部：保持“图标 + 工具名 + 成功/失败”。
- 成功态：在摘要下方渲染“详情”。
  - 显示 `operationType` 的中文标签（创建/更新/删除/查询）。
  - 显示 `rowsAffected` 与 `affectedIds`（最多展示前 N 条，超出以“+N”标识）。
  - create：展示 `createdCount / createdIds`，如有 `preview` 仅显示最小字段（标题、时间）。
  - update：为 `changes` 渲染“对象ID + changedFields 列表 + diff（before→after）”，并仅显示发生改动的字段；可附带 `title` 便于识别。
  - delete：展示 `deletedCount / deletedIds`，可附带最小 `titles`。
  - query：不展示详情区块。
  - 当 `truncated` 为真时，底部提示“为保证性能已部分省略”。
- 失败态：保持显示 `toolError`。

交互与样式：
- 详情区默认折叠，仅显示一行摘要 + “展开”指示；点击展开后显示完整详情；再次点击收起。
- 避免消息过高：详情区限定最大高度（如 260px）并内部滚动。

消息流对接：
- 父级 `l-message-list.vue` 已将 `message.result` 作为 `tool-result` 传入本组件；在组件内以 `toolResult.details` 获取详情（查询类不会携带 `details`）。
- 无 `details` 或为查询类时保持现有摘要展示，不渲染详情区块。

## 示例

后端 SSE 示例（create）：
```json
{
  "type": "tool_execution_complete",
  "sessionId": "s_001",
  "data": {
    "toolName": "task_create",
    "result": {
      "message": "新增任务成功；共 2 条数据",
      "executionTime": 85,
      "dataCount": 2,
      "errCode": 0,
      "details": {
        "operationType": "create",
        "summary": "创建 2 条任务",
        "rowsAffected": 2,
        "affectedIds": ["t1", "t2"],
        "createdIds": ["t1", "t2"],
        "createdCount": 2,
        "preview": [
          { "_id": "t1", "title": "学习英语" },
          { "_id": "t2", "title": "晨跑5km" }
        ]
      }
    },
    "success": true,
    "toolCallId": "call_abc"
  },
  "timestamp": "2025-05-24T02:26:08.286Z"
}
```

前端展示要点：
- 标题：task_create 执行成功
- 摘要：新增任务成功；共 2 条数据
- 详情（展开）：创建 2 条任务、受影响ID（t1, t2）、预览表格（_id, title）

## 验收标准

- 后端：
  - 非查询操作返回 `result.details`，字段与上面的规范一致；超过阈值时 `truncated=true`；仅返回改动字段的最小必要集。
  - 查询操作不返回 `details`，严禁回传列表/预览等大体量数据；仅允许摘要与计数类字段。
  - 原有字段保持向后兼容，不破坏现有消费者。
- 前端：
  - 有 `details` 时显示“详情”区块并可折叠。
  - 各 operationType 的关键字段展示正确；超出部分以“+N”或滚动处理；存在 `truncated` 时有提示。
  - 无 `details` 时表现与当前一致。

## 技术方案

- 后端：
  - 在 `executeToolCall` 汇总阶段新增 `buildDetails(operationType, result)`，对工具原始返回进行轻量提要与限流截断。
  - 依据工具名或工具自身返回的 `operationType` 判断操作类型，统一映射为 `details`。
  - 关键常量（如每类最多 20 条、字符串截断 120 字符）集中于配置，便于后续调优。

- 前端：
  - 在 `l-message-bubble.vue` 内增加“详情”区块的渲染与折叠交互（基于现有 `tool_result` 分支）。
  - 为避免改动父层数据结构，仅读取 `toolResult.details` 渲染，不改动消息生成逻辑。
  - 样式：为详情容器设置最大高度与滚动，防止撑高消息流。

## 风险评估

- SSE 体积膨胀：通过条数与字段截断控制；默认不回传完整对象快照。
- 工具多样性：不同工具返回结构差异较大；通过 `buildDetails` 统一抽象，逐步适配关键工具。
- 前端可用性：过多详情导致阅读负担；默认折叠与简要摘要并存，用户自愿展开查看。



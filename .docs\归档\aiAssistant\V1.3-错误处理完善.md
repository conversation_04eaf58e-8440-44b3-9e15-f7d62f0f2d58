# V1.3 - 错误处理完善与性能监控

## 1. 改动概述

### 1.1 核心目标
- 完善分层错误处理机制，采用MCP的错误处理思想
- 实现智能重试和降级策略，提升系统健壮性
- 添加性能监控和指标收集，支持系统优化
- 优化用户体验，提供更友好的错误反馈

### 1.2 解决的问题
- **V1.2遗留问题**：缺少完善的错误处理和恢复机制
- **稳定性问题**：外部API调用失败时缺少降级策略
- **监控盲点**：缺少性能指标和执行状态监控
- **用户体验**：错误信息不够友好和具体

### 1.3 基于V1.2的改进
- 在现有动态参数解析基础上增加错误处理
- 完善执行引擎的容错能力
- 添加全面的监控和日志记录

## 2. 技术方案

### 2.1 分层错误处理器（采用MCP错误处理思想）

```javascript
// 新增：分层错误处理器
class EnhancedErrorHandler {
  static async handleToolError(error, step, context, sseChannel) {
    const errorInfo = {
      stepId: step.stepId,
      toolName: step.toolName,
      error: error.message,
      timestamp: Date.now(),
      retryCount: step.retryCount
    }

    // 1. 参数验证错误（不可重试）
    if (error.message.includes('参数验证失败')) {
      await sseChannel.write({
        type: 'step_error',
        ...errorInfo,
        errorType: 'validation_error',
        recoverable: false,
        suggestions: this.generateValidationSuggestions(error)
      })
      throw error
    }

    // 2. 工具执行错误（可重试）
    if (step.retryCount < step.maxRetries) {
      await this.retryStep(step, context, sseChannel)
      return 'retry'
    }

    // 3. 降级处理
    const fallbackResult = await this.attemptFallback(step, context)
    if (fallbackResult) {
      await sseChannel.write({
        type: 'step_fallback',
        stepId: step.stepId,
        fallbackResult,
        originalError: error.message,
        timestamp: Date.now()
      })
      return fallbackResult
    }

    // 4. 最终失败
    await sseChannel.write({
      type: 'step_error',
      ...errorInfo,
      errorType: 'execution_error',
      recoverable: false,
      suggestions: this.generateErrorSuggestions(error, step)
    })
    
    throw new Error(`工具执行失败：${error.message}`)
  }

  static async retryStep(step, context, sseChannel) {
    step.retryCount++
    
    await sseChannel.write({
      type: 'step_retry',
      stepId: step.stepId,
      retryCount: step.retryCount,
      maxRetries: step.maxRetries,
      timestamp: Date.now()
    })

    // 指数退避重试
    const delay = Math.min(1000 * Math.pow(2, step.retryCount - 1), 10000)
    await new Promise(resolve => setTimeout(resolve, delay))
  }

  static async attemptFallback(step, context) {
    // 根据工具类型尝试降级方案
    switch (step.toolName) {
      case 'getProjects':
        // 如果获取项目列表失败，尝试使用缓存或默认项目
        return this.getFallbackProjects(context)
      
      case 'getTasks':
        // 如果获取任务失败，返回空列表但保持流程继续
        return { 
          tasks: [], 
          metadata: { 
            fallback: true, 
            message: '暂时无法获取任务列表，请稍后重试' 
          } 
        }
      
      default:
        return null
    }
  }

  static generateValidationSuggestions(error) {
    const suggestions = []
    
    if (error.message.includes('缺少必需参数')) {
      suggestions.push('请检查输入是否包含必要的信息')
    }
    
    if (error.message.includes('格式不符合要求')) {
      suggestions.push('请使用正确的格式输入')
    }
    
    return suggestions
  }

  static generateErrorSuggestions(error, step) {
    const suggestions = []
    
    if (error.message.includes('API rate limit')) {
      suggestions.push('请稍后重试，API调用频率过高')
    }
    
    if (error.message.includes('unauthorized')) {
      suggestions.push('请检查API密钥配置')
    }
    
    if (step.toolName === 'getProjects' && error.message.includes('not found')) {
      suggestions.push('请确认您有访问项目列表的权限')
    }
    
    return suggestions
  }

  static async getFallbackProjects(context) {
    // 返回默认项目或缓存项目
    return {
      projects: [
        { id: 'default', name: '默认项目', description: '系统默认项目' }
      ],
      metadata: { 
        fallback: true, 
        source: 'default',
        message: '使用默认项目，请稍后重试获取完整项目列表'
      }
    }
  }
}
```

### 2.2 性能监控器

```javascript
// 新增：性能监控器
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      // 执行计划相关
      planGenerationTime: [],
      planExecutionTime: [],
      averageStepTime: [],
      
      // 工具调用相关
      toolCallSuccessRate: 0,
      toolCallCount: 0,
      toolCallFailures: 0,
      averageToolResponseTime: [],
      retryRate: 0,
      
      // 参数解析相关
      parameterResolutionTime: [],
      dynamicReferenceCount: 0,
      resolutionSuccessRate: 0,
      
      // 错误统计
      errorsByType: {},
      errorsByTool: {},
      
      // 用户体验指标
      userSatisfactionScore: 0,
      taskCompletionRate: 0
    }
  }

  // 记录执行计划生成时间
  recordPlanGeneration(startTime, endTime) {
    const duration = endTime - startTime
    this.metrics.planGenerationTime.push(duration)
    
    // 保持最近100条记录
    if (this.metrics.planGenerationTime.length > 100) {
      this.metrics.planGenerationTime.shift()
    }
  }

  // 记录工具调用结果
  recordToolCall(toolName, success, responseTime, retryCount = 0) {
    this.metrics.toolCallCount++
    
    if (success) {
      this.metrics.averageToolResponseTime.push(responseTime)
    } else {
      this.metrics.toolCallFailures++
      this.metrics.errorsByTool[toolName] = (this.metrics.errorsByTool[toolName] || 0) + 1
    }
    
    if (retryCount > 0) {
      this.metrics.retryRate = (this.metrics.retryRate * (this.metrics.toolCallCount - 1) + 1) / this.metrics.toolCallCount
    }
    
    // 更新成功率
    this.metrics.toolCallSuccessRate = (this.metrics.toolCallCount - this.metrics.toolCallFailures) / this.metrics.toolCallCount
  }

  // 记录错误
  recordError(errorType, errorMessage, context = {}) {
    this.metrics.errorsByType[errorType] = (this.metrics.errorsByType[errorType] || 0) + 1
    
    // 记录详细错误信息用于分析
    console.log('Performance Monitor - Error Recorded:', {
      type: errorType,
      message: errorMessage,
      context: context,
      timestamp: Date.now()
    })
  }

  // 记录参数解析性能
  recordParameterResolution(startTime, endTime, dynamicRefCount, success) {
    const duration = endTime - startTime
    this.metrics.parameterResolutionTime.push(duration)
    this.metrics.dynamicReferenceCount += dynamicRefCount
    
    // 更新解析成功率
    const totalResolutions = this.metrics.parameterResolutionTime.length
    const currentSuccessRate = this.metrics.resolutionSuccessRate
    this.metrics.resolutionSuccessRate = (currentSuccessRate * (totalResolutions - 1) + (success ? 1 : 0)) / totalResolutions
  }

  // 获取性能报告
  getPerformanceReport() {
    return {
      summary: {
        totalToolCalls: this.metrics.toolCallCount,
        successRate: this.metrics.toolCallSuccessRate,
        averageResponseTime: this.calculateAverage(this.metrics.averageToolResponseTime),
        retryRate: this.metrics.retryRate
      },
      planGeneration: {
        averageTime: this.calculateAverage(this.metrics.planGenerationTime),
        samples: this.metrics.planGenerationTime.length
      },
      parameterResolution: {
        averageTime: this.calculateAverage(this.metrics.parameterResolutionTime),
        successRate: this.metrics.resolutionSuccessRate,
        totalDynamicReferences: this.metrics.dynamicReferenceCount
      },
      errors: {
        byType: this.metrics.errorsByType,
        byTool: this.metrics.errorsByTool
      },
      timestamp: Date.now()
    }
  }

  calculateAverage(array) {
    if (array.length === 0) return 0
    return array.reduce((sum, val) => sum + val, 0) / array.length
  }

  // 重置指标
  reset() {
    this.metrics = {
      planGenerationTime: [],
      planExecutionTime: [],
      averageStepTime: [],
      toolCallSuccessRate: 0,
      toolCallCount: 0,
      toolCallFailures: 0,
      averageToolResponseTime: [],
      retryRate: 0,
      parameterResolutionTime: [],
      dynamicReferenceCount: 0,
      resolutionSuccessRate: 0,
      errorsByType: {},
      errorsByTool: {},
      userSatisfactionScore: 0,
      taskCompletionRate: 0
    }
  }
}

// 全局性能监控实例
const globalPerformanceMonitor = new PerformanceMonitor()
```

### 2.3 增强的执行引擎

```javascript
// 升级：增强的执行引擎（集成错误处理和监控）
async function executeRobustPlan(executionPlan, context, sseChannel) {
  const planStartTime = Date.now()
  
  try {
    // 推送执行计划
    await sseChannel.write({
      type: 'execution_plan',
      plan: {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
        estimatedTotalTime: executionPlan.estimatedTotalTime
      },
      timestamp: Date.now()
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
          estimatedTime: step.estimatedTime
        },
        timestamp: Date.now()
      })

      step.status = 'executing'
      const stepStartTime = Date.now()

      try {
        // 参数解析监控
        const paramResolutionStart = Date.now()
        const resolvedParams = await DynamicParameterResolver.resolveParameters(step, context)
        const paramResolutionEnd = Date.now()
        
        // 记录参数解析性能
        const dynamicRefCount = this.countDynamicReferences(step.parameters)
        globalPerformanceMonitor.recordParameterResolution(
          paramResolutionStart, 
          paramResolutionEnd, 
          dynamicRefCount, 
          true
        )
        
        // 参数验证
        const validatedParams = ParameterValidator.validate(step.toolName, resolvedParams)
        
        // 工具调用监控
        const toolCallStart = Date.now()
        const result = await callRealTool(step.toolName, validatedParams)
        const toolCallEnd = Date.now()
        
        // 记录工具调用性能
        globalPerformanceMonitor.recordToolCall(
          step.toolName, 
          true, 
          toolCallEnd - toolCallStart, 
          step.retryCount
        )
        
        // 存储结果到上下文
        context.setStepResult(step.stepId, result)
        
        step.status = 'completed'
        step.executionTime = Date.now() - stepStartTime

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          executionTime: step.executionTime,
          contextUpdates: getContextUpdates(context),
          timestamp: Date.now()
        })

      } catch (error) {
        step.executionTime = Date.now() - stepStartTime
        
        // 记录错误
        globalPerformanceMonitor.recordError('tool_execution', error.message, {
          stepId: step.stepId,
          toolName: step.toolName,
          retryCount: step.retryCount
        })
        
        // 记录失败的工具调用
        globalPerformanceMonitor.recordToolCall(step.toolName, false, step.executionTime, step.retryCount)

        // 使用增强的错误处理器
        const handleResult = await EnhancedErrorHandler.handleToolError(error, step, context, sseChannel)
        
        if (handleResult === 'retry') {
          // 重试当前步骤
          i-- // 重新执行当前步骤
          continue
        } else if (handleResult && typeof handleResult === 'object') {
          // 使用降级结果
          context.setStepResult(step.stepId, handleResult)
          step.status = 'completed_with_fallback'
        } else {
          // 最终失败
          step.status = 'failed'
          step.error = error.message
          throw error
        }
      }
    }

    executionPlan.status = 'completed'
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - executionPlan.startTime

    // 记录执行计划性能
    globalPerformanceMonitor.recordPlanGeneration(planStartTime, executionPlan.endTime)

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      summary: generateExecutionSummary(executionPlan, context),
      performanceMetrics: globalPerformanceMonitor.getPerformanceReport(),
      timestamp: Date.now()
    })

    return executionPlan

  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - executionPlan.startTime

    // 记录执行失败
    globalPerformanceMonitor.recordError('plan_execution', error.message, {
      planId: executionPlan.planId,
      totalSteps: executionPlan.totalSteps
    })

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      performanceMetrics: globalPerformanceMonitor.getPerformanceReport(),
      timestamp: Date.now()
    })
    
    throw error
  }
}

// 计算动态引用数量
function countDynamicReferences(parameters) {
  let count = 0
  for (const value of Object.values(parameters)) {
    if (typeof value === 'string') {
      if (value.startsWith('$context.') || value.startsWith('$step.') || value.startsWith('$filter(')) {
        count++
      }
    }
  }
  return count
}
```

## 3. 新增SSE消息类型

```javascript
// V1.3版本新增的SSE消息类型
const V13_SSE_MESSAGE_TYPES = {
  // V1.2已有类型保持不变
  
  // V1.3新增类型
  'step_retry': '步骤重试',
  'step_fallback': '步骤降级',
  'performance_update': '性能指标更新',
  'system_health': '系统健康状态'
}
```

## 4. chatStreamSSE函数最终版本

```javascript
// 最终版本的chatStreamSSE函数修改
async chatStreamSSE(params) {
  // ... 现有参数处理和意图识别逻辑保持不变 ...

  try {
    // ... 意图识别逻辑 ...

    if (intentType && intentType !== 'chat') {
      const context = new ExecutionContextManager(generateUUID(), message)
      
      // 使用智能执行计划生成器
      const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)
      
      if (executionPlan.totalSteps > 0) {
        // 使用增强的执行引擎
        await executeRobustPlan(executionPlan, context, sseChannel)
        
        return {
          errCode: 0,
          errMsg: 'success',
          data: {
            type: 'task_executed',
            intentType: intentType,
            executionPlan: executionPlan,
            contextData: Array.from(context.contextData.keys()),
            executionTime: executionPlan.totalExecutionTime,
            performanceMetrics: globalPerformanceMonitor.getPerformanceReport()
          }
        }
      }
    }

    // ... 现有的chat处理逻辑保持不变 ...

  } catch (error) {
    // 增强的错误处理
    console.error('chatStreamSSE错误：', error)
    
    // 记录系统级错误
    globalPerformanceMonitor.recordError('system', error.message, {
      userInput: message,
      intentType: intentType
    })

    // 尝试发送错误消息给前端
    try {
      if (channel) {
        const sseChannel = uniCloud.deserializeSSEChannel(channel)
        await sseChannel.end({
          type: 'system_error',
          error: error.message || '系统处理失败',
          suggestions: ['请稍后重试', '如果问题持续，请联系技术支持'],
          performanceMetrics: globalPerformanceMonitor.getPerformanceReport(),
          timestamp: Date.now()
        })
      }
    } catch (channelError) {
      console.error('SSE推送错误：', channelError)
    }

    return {
      errCode: 'SYSTEM_ERROR',
      errMsg: error.message || '系统处理失败',
      data: {
        type: 'system_error',
        performanceMetrics: globalPerformanceMonitor.getPerformanceReport()
      }
    }
  }
}
```

## 5. 测试用例

### 5.1 错误处理测试
```javascript
test('错误处理和重试机制', async () => {
  // 模拟API调用失败
  const mockError = new Error('API调用超时')
  
  const step = {
    stepId: 'test-step',
    toolName: 'getTasks',
    retryCount: 0,
    maxRetries: 3
  }
  
  const result = await EnhancedErrorHandler.handleToolError(mockError, step, context, mockSSEChannel)
  expect(result).toBe('retry')
  expect(step.retryCount).toBe(1)
})
```

### 5.2 性能监控测试
```javascript
test('性能监控功能', () => {
  const monitor = new PerformanceMonitor()
  
  // 记录工具调用
  monitor.recordToolCall('getTasks', true, 1500, 0)
  monitor.recordToolCall('getProjects', false, 3000, 2)
  
  const report = monitor.getPerformanceReport()
  expect(report.summary.totalToolCalls).toBe(2)
  expect(report.summary.successRate).toBe(0.5)
})
```

### 5.3 降级策略测试
```javascript
test('降级策略', async () => {
  const step = { toolName: 'getProjects' }
  const context = new ExecutionContextManager('test', 'test input')
  
  const fallbackResult = await EnhancedErrorHandler.attemptFallback(step, context)
  
  expect(fallbackResult).toBeDefined()
  expect(fallbackResult.metadata.fallback).toBe(true)
})
```

## 6. 部署指南

### 6.1 部署前检查
- V1.2版本运行稳定
- 监控系统已准备就绪
- 错误日志收集机制已建立

### 6.2 部署步骤
1. **代码集成**：集成错误处理器和性能监控器
2. **错误处理测试**：验证各种错误场景的处理
3. **性能基准测试**：建立性能指标基线
4. **降级策略测试**：验证降级机制的有效性
5. **监控配置**：配置性能指标收集和告警
6. **灰度发布**：50%用户先行体验
7. **全量发布**：确认稳定后全量发布

## 7. 风险评估

### 7.1 性能风险
- **风险**：监控代码可能影响性能
- **应对**：异步记录，最小化性能影响

### 7.2 稳定性风险
- **风险**：错误处理逻辑可能引入新问题
- **应对**：充分的测试覆盖和监控

## 8. 项目总结

V1.3版本完成后，整个MCP改造项目将实现：

### 8.1 核心能力
- ✅ 标准化的工具注册系统
- ✅ 智能的执行上下文管理
- ✅ 强大的动态参数解析
- ✅ 完善的错误处理和降级
- ✅ 全面的性能监控

### 8.2 技术成果
- 从简单意图识别升级为完整的任务执行系统
- 借鉴MCP设计理念，建立了先进的架构模式
- 实现了高可用、高性能的智能助手系统

### 8.3 后续演进
- 可基于性能数据进行持续优化
- 可扩展更多工具和功能模块
- 可集成更多第三方服务和API

这标志着chatStreamSSE从一个简单的对话接口成功演进为一个功能完整、性能优异、高度可靠的智能任务执行系统。

<template>
  <view class="flomo-page">
    <view class="status-bar-placeholder"></view>

    <!-- 标签区域 -->
    <view class="tags-section">
      <view class="tags-container" :class="{ expanded: isTagsExpanded }">
        <!-- 加载状态 -->
        <view v-if="isLoadingTags" class="tags-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <text>加载标签中...</text>
        </view>

        <scroll-view class="tags-scroll" scroll-x="true" :show-scrollbar="false" v-else-if="!isTagsExpanded">
          <view class="tags-scroll-content">
            <view
              v-for="tag in tags"
              :key="tag._id"
              class="tag-item"
              :class="{ active: selectedTags.includes(tag._id) }"
              @click="toggleTag(tag._id)"
              @longpress="editTag(tag)"
            >
              <text>#{{ tag.name }}</text>
            </view>
            <view class="add-tag-btn" @click="showAddTagModal = true">
              <i class="fas fa-plus"></i>
              <text>添加</text>
            </view>
          </view>
        </scroll-view>

        <!-- 展开后的标签网格 -->
        <view class="tags-grid" v-else>
          <view
            v-for="tag in tags"
            :key="tag._id"
            class="tag-item"
            :class="{ active: selectedTags.includes(tag._id) }"
            @click="toggleTag(tag._id)"
            @longpress="editTag(tag)"
          >
            <text>#{{ tag.name }}</text>
          </view>
          <view class="add-tag-btn" @click="showAddTagModal = true">
            <i class="fas fa-plus"></i>
            <text>添加</text>
          </view>
        </view>

        <!-- 展开/收起按钮 -->
        <view class="expand-btn" @click="toggleTagsExpand" v-if="!isLoadingTags">
          <i :class="isTagsExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
        </view>
      </view>
    </view>

    <!-- 日记输入区域 -->
    <view class="diary-input-section" v-if="!showAIResponse">
      <view class="input-container">
        <textarea
          v-model="diaryContent"
          class="diary-textarea"
          placeholder="记录此刻的想法..."
          :maxlength="5000"
          :show-count="true"
          :auto-height="true"
          :cursor-spacing="20"
        />
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="save-btn" @click="analyzeDiary" :class="{ disabled: !diaryContent.trim() }">
          <i class="fas fa-save"></i>
          <text>AI 分析</text>
        </view>
        <view class="clear-btn" @click="clearContent" v-if="diaryContent.trim()">
          <i class="fas fa-trash-alt"></i>
          <text>清空</text>
        </view>
      </view>
    </view>

    <!-- AI 响应展示区域 -->
    <view class="ai-response-section" v-if="showAIResponse">
      <view class="response-header">
        <text class="response-title">AI 分析结果</text>
        <view class="new-entry-btn" @click="startNewEntry">
          <i class="fas fa-plus"></i>
          <text>新记录</text>
        </view>
      </view>

      <view class="response-content" :class="{ processing: isProcessing }">
        <!-- 处理中状态 -->
        <view v-if="isProcessing" class="loading-content">
          <i class="fas fa-spinner fa-spin" style="font-size: 48rpx; color: #2196f3; margin-bottom: 20rpx"></i>
          <text class="loading-text">AI 正在分析中，请稍候...</text>
        </view>

        <!-- AI 响应结果 -->
        <view v-else>
          <view
            v-for="(item, index) in aiResponseData"
            :key="index"
            class="response-item"
            :class="{ 'show-actions': activeItemIndex === index }"
            @click="toggleItemActions(index)"
          >
            <view class="item-content">
              <view class="item-text" v-html="formatResponseText(item)"></view>
            </view>
            <view class="item-actions">
              <view class="action-btn edit-btn" @click.stop="editResponseItem(index)">
                <i class="fas fa-edit"></i>
              </view>
              <view class="action-btn delete-btn" @click.stop="deleteResponseItem(index)">
                <i class="fas fa-trash-alt"></i>
              </view>
            </view>
          </view>

          <!-- 提交按钮 -->
          <view class="submit-section">
            <view class="submit-btn" @click="submitAnalysis" :class="{ disabled: aiResponseData.length === 0 }">
              <i class="fas fa-paper-plane"></i>
              <text>提交分析结果</text>
            </view>
          </view>
        </view>
      </view>
    </view>

         <!-- 添加标签模态框 -->
     <u-popup v-model="showAddTagModal" mode="center" border-radius="14" width="80%" :closeable="!isAddingTag">
      <view class="add-tag-modal">
        <view class="modal-header">
          <text class="modal-title">添加标签</text>
        </view>

        <view class="modal-content">
          <view class="form-item">
            <text class="form-label">标签名称 *</text>
            <input v-model="newTag.name" class="form-input" placeholder="请输入标签名称" :maxlength="20" />
          </view>

          <view class="form-item">
            <text class="form-label">标签描述</text>
            <textarea
              v-model="newTag.description"
              class="form-textarea"
              placeholder="请输入标签描述（可选）"
              :maxlength="100"
              :auto-height="true"
            />
          </view>
        </view>

        <view class="modal-footer">
          <view class="modal-btn cancel-btn" @click="cancelAddTag">
            <text>取消</text>
          </view>
                     <view class="modal-btn confirm-btn" :class="{ disabled: !newTag.name.trim() || isAddingTag }" @click="confirmAddTag">
             <view v-if="isAddingTag" class="loading-content">
               <i class="fas fa-spinner fa-spin"></i>
               <text>添加中</text>
             </view>
             <text v-else>确认</text>
           </view>
        </view>
      </view>
    </u-popup>

         <!-- 编辑标签模态框 -->
     <u-popup v-model="showEditTagModal" mode="center" border-radius="14" width="80%" :closeable="!isEditingTag">
      <view class="edit-tag-modal">
        <view class="modal-header">
          <text class="modal-title">编辑标签</text>
        </view>

        <view class="modal-content">
          <view class="form-item">
            <text class="form-label">标签名称 *</text>
            <input v-model="editingTag.name" class="form-input" placeholder="请输入标签名称" :maxlength="20" />
          </view>

          <view class="form-item">
            <text class="form-label">标签描述</text>
            <textarea
              v-model="editingTag.description"
              class="form-textarea"
              placeholder="请输入标签描述（可选）"
              :maxlength="100"
              :auto-height="true"
            />
          </view>
        </view>

        <view class="modal-footer">
          <view class="modal-btn delete-btn" @click="confirmDeleteTag" :class="{ disabled: isEditingTag }">
            <i class="fas fa-trash-alt"></i>
            <text>删除</text>
          </view>
          <view class="modal-btn cancel-btn" @click="cancelEditTag">
            <text>取消</text>
          </view>
          <view class="modal-btn confirm-btn" :class="{ disabled: !editingTag.name.trim() || isEditingTag }" @click="confirmEditTag">
            <view v-if="isEditingTag" class="loading-content">
              <i class="fas fa-spinner fa-spin"></i>
              <text>更新中</text>
            </view>
            <text v-else>确认</text>
          </view>
        </view>
      </view>
    </u-popup>



    <!-- 确认清空弹窗 -->
    <z-confirm-modal
      v-model:visible="showClearConfirm"
      title="清空内容"
      message="确定要清空当前输入的内容吗？此操作不可恢复。"
      confirm-text="清空"
      cancel-text="取消"
      type="danger"
      @confirm="doClearContent"
    />

    <!-- 确认删除标签弹窗 -->
    <z-confirm-modal
      v-model:visible="showDeleteTagConfirm"
      title="删除标签"
      :message="deleteTagMessage"
      confirm-text="删除"
      cancel-text="取消"
      type="danger"
      @confirm="doDeleteTag"
    />

    <!-- 编辑 AI 响应结果模态框 -->
    <u-popup v-model="showEditResponseModal" mode="center" border-radius="14" width="80%" :closeable="true">
      <view class="edit-response-modal">
        <view class="modal-header">
          <text class="modal-title">编辑分析结果</text>
        </view>
        <view class="modal-content">
          <view class="form-item">
            <text class="form-label">分析内容</text>
            <textarea
              v-model="editingResponse.content"
              class="form-textarea"
              placeholder="请输入分析内容"
              :maxlength="500"
              :auto-height="true"
            />
          </view>
        </view>
        <view class="modal-footer">
          <view class="modal-btn cancel-btn" @click="cancelEditResponse">
            <text>取消</text>
          </view>
          <view
            class="modal-btn confirm-btn"
            :class="{ disabled: !editingResponse.content.trim() }"
            @click="confirmEditResponse"
          >
            <text>确认</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 确认删除 AI 响应结果弹窗 -->
    <z-confirm-modal
      v-model:visible="showDeleteResponseConfirm"
      title="删除分析结果"
      message="确定要删除这个分析结果吗？此操作不可恢复。"
      confirm-text="删除"
      cancel-text="取消"
      type="danger"
      @confirm="doDeleteResponse"
    />
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { router } from '@/utils/tools'
import ZConfirmModal from '@/components/z-confirm-modal.vue'
import dayjs from 'dayjs'

// 云函数对象初始化
const aiApi = uniCloud.importObject('ai', { customUI: true })

// 数据定义
const tags = ref([])
const selectedTags = ref([])
const diaryContent = ref('')
const showAddTagModal = ref(false)
const showEditTagModal = ref(false)

const showClearConfirm = ref(false)
const showDeleteTagConfirm = ref(false)
const isTagsExpanded = ref(false)
const isLoadingTags = ref(false)
const isAddingTag = ref(false)
const isEditingTag = ref(false)

// AI 响应相关状态
const showAIResponse = ref(false)
const aiResponseData = ref([])
const isProcessing = ref(false)
const showEditResponseModal = ref(false)
const showDeleteResponseConfirm = ref(false)
const editingResponse = ref({
  index: -1,
  content: '',
})
const deletingResponseIndex = ref(-1)
const activeItemIndex = ref(-1)

// 新标签表单
const newTag = ref({
  name: '',
  description: '',
})

// 编辑标签表单
const editingTag = ref({
  _id: '',
  name: '',
  description: '',
})

// 当前操作的标签
const currentTag = ref(null)

// 计算属性
const deleteTagMessage = computed(() => {
  if (!currentTag.value) return '确定要删除这个标签吗？此操作不可恢复。'
  return `确定要删除标签"#${currentTag.value.name}"吗？此操作不可恢复。`
})

// 标签操作
const toggleTag = (tagId) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }
}

// 展开/收起标签
const toggleTagsExpand = () => {
  isTagsExpanded.value = !isTagsExpanded.value
}

// 编辑标签
const editTag = (tag) => {
  currentTag.value = tag
  editingTag.value = {
    _id: tag._id,
    name: tag.name,
    description: tag.description || '',
  }
  showEditTagModal.value = true
}

// 确认删除标签
const confirmDeleteTag = () => {
  showEditTagModal.value = false
  showDeleteTagConfirm.value = true
}

// 执行删除标签
const doDeleteTag = async () => {
  if (!currentTag.value) return

  try {
    // 调用云函数删除标签
    const response = await aiApi.flomoLabelManager({
      action: 'delete',
      data: { _id: currentTag.value._id },
      userId: 'test_user_001', // 使用测试用户ID
    })

    if (response.errCode === 0) {
      // 从本地列表中移除标签
      const index = tags.value.findIndex((tag) => tag._id === currentTag.value._id)
      if (index > -1) {
        tags.value.splice(index, 1)

        // 如果删除的标签在已选中列表中，也要移除
        const selectedIndex = selectedTags.value.indexOf(currentTag.value._id)
        if (selectedIndex > -1) {
          selectedTags.value.splice(selectedIndex, 1)
        }

        uni.showToast({
          title: '标签删除成功',
          icon: 'success',
        })
      }
    } else {
      throw new Error(response.errMsg || '删除失败')
    }

    showDeleteTagConfirm.value = false
    currentTag.value = null
  } catch (error) {
    console.error('删除标签失败：', error)
    uni.showToast({
      title: error.message || '删除失败，请重试',
      icon: 'none',
    })
  }
}

// 添加标签相关方法
const cancelAddTag = () => {
  showAddTagModal.value = false
  newTag.value = { name: '', description: '' }
}

const confirmAddTag = async () => {
  if (!newTag.value.name.trim()) {
    uni.showToast({
      title: '请输入标签名称',
      icon: 'none',
    })
    return
  }

  try {
    isAddingTag.value = true
    
    // 调用云函数创建标签
    const response = await aiApi.flomoLabelManager({
      action: 'create',
      data: {
        name: newTag.value.name.trim(),
        description: newTag.value.description.trim(),
      },
      userId: 'test_user_001', // 使用测试用户ID
    })

    if (response.errCode === 0 && response.data) {
      // 将新标签添加到本地列表
      tags.value.push(response.data)

      uni.showToast({
        title: '标签添加成功',
        icon: 'success',
      })

      cancelAddTag()
    } else {
      throw new Error(response.errMsg || '添加失败')
    }
  } catch (error) {
    console.error('添加标签失败：', error)
    uni.showToast({
      title: error.message || '添加失败，请重试',
      icon: 'none',
    })
  } finally {
    isAddingTag.value = false
  }
}

// 编辑标签相关方法
const cancelEditTag = () => {
  showEditTagModal.value = false
  editingTag.value = { _id: '', name: '', description: '' }
}

const confirmEditTag = async () => {
  if (!editingTag.value.name.trim()) {
    uni.showToast({
      title: '请输入标签名称',
      icon: 'none',
    })
    return
  }

  try {
    isEditingTag.value = true
    
    // 调用云函数更新标签
    const response = await aiApi.flomoLabelManager({
      action: 'update',
      data: {
        _id: editingTag.value._id,
        name: editingTag.value.name.trim(),
        description: editingTag.value.description.trim(),
      },
      userId: 'test_user_001', // 使用测试用户ID
    })

    if (response.errCode === 0) {
      // 更新本地标签数据
      const index = tags.value.findIndex((tag) => tag._id === editingTag.value._id)
      if (index > -1) {
        tags.value[index] = {
          ...tags.value[index],
          name: editingTag.value.name.trim(),
          description: editingTag.value.description.trim(),
        }

        uni.showToast({
          title: '标签更新成功',
          icon: 'success',
        })

        cancelEditTag()
      }
    } else {
      throw new Error(response.errMsg || '更新失败')
    }
  } catch (error) {
    console.error('更新标签失败：', error)
    uni.showToast({
      title: error.message || '更新失败，请重试',
      icon: 'none',
    })
  } finally {
    isEditingTag.value = false
  }
}

// AI 分析功能已集成到云函数中

// AI 分析日记
const analyzeDiary = async () => {
  if (!diaryContent.value.trim()) {
    uni.showToast({
      title: '请输入日记内容',
      icon: 'none',
    })
    return
  }

  try {
    // 显示处理中状态
    isProcessing.value = true
    showAIResponse.value = true

    // 准备要发送的数据
    const selectedTagNames = selectedTags.value
      .map((tagId) => {
        const tag = tags.value.find((t) => t._id === tagId)
        return tag ? tag.name : ''
      })
      .filter((name) => name)

    // 如果没有选择标签，则使用全部标签
    const tagsToSend = selectedTags.value.length > 0 ? selectedTags.value : tags.value.map((tag) => tag._id)

    const dataToSend = {
      content: diaryContent.value.trim(),
      tags: JSON.stringify(
        tagsToSend
          .map((tagId) => {
            const tag = tags.value.find((t) => t._id === tagId)
            return tag
              ? {
                  title: tag.name,
                  description: tag.description || '',
                }
              : null
          })
          .filter((tag) => tag)
      ),
    }

    // 调用云函数进行 AI 分析
    const response = await aiApi.flomoAnalysis(dataToSend)

    if (response.errCode === 0 && response.data) {
      // 设置 AI 响应数据
      aiResponseData.value = response.data

      uni.showToast({
        title: '分析完成',
        icon: 'success',
      })
    } else {
      throw new Error(response.errMsg || 'AI 分析失败')
    }
  } catch (error) {
    console.error('AI 分析失败：', error)
    uni.showToast({
      title: '分析失败，请重试',
      icon: 'none',
    })

    // 显示错误信息
    aiResponseData.value = ['AI 分析遇到问题，请检查网络连接后重试', '如果问题持续存在，请联系技术支持']
  } finally {
    isProcessing.value = false
  }
}

// 清空内容
const clearContent = () => {
  showClearConfirm.value = true
}

const doClearContent = () => {
  diaryContent.value = ''
  selectedTags.value = []
  showClearConfirm.value = false
}

// 开始新记录
const startNewEntry = () => {
  showAIResponse.value = false
  diaryContent.value = ''
  selectedTags.value = []
  aiResponseData.value = []
  isProcessing.value = false
  activeItemIndex.value = -1
}

// 编辑 AI 响应结果
const editResponseItem = (index) => {
  editingResponse.value = {
    index: index,
    content: aiResponseData.value[index],
  }
  showEditResponseModal.value = true
}

// 取消编辑 AI 响应结果
const cancelEditResponse = () => {
  showEditResponseModal.value = false
  editingResponse.value = { index: -1, content: '' }
}

// 确认编辑 AI 响应结果
const confirmEditResponse = () => {
  if (!editingResponse.value.content.trim()) {
    uni.showToast({
      title: '请输入分析内容',
      icon: 'none',
    })
    return
  }

  try {
    const index = editingResponse.value.index
    aiResponseData.value[index] = editingResponse.value.content.trim()

    uni.showToast({
      title: '更新成功',
      icon: 'success',
    })

    cancelEditResponse()
  } catch (error) {
    uni.showToast({
      title: '更新失败，请重试',
      icon: 'none',
    })
  }
}

// 删除 AI 响应结果
const deleteResponseItem = (index) => {
  deletingResponseIndex.value = index
  showDeleteResponseConfirm.value = true
}

// 执行删除 AI 响应结果
const doDeleteResponse = () => {
  try {
    const index = deletingResponseIndex.value
    if (index >= 0 && index < aiResponseData.value.length) {
      aiResponseData.value.splice(index, 1)

      // 如果删除的是当前激活的项目，重置激活状态
      if (activeItemIndex.value === index) {
        activeItemIndex.value = -1
      } else if (activeItemIndex.value > index) {
        // 如果删除的项目在当前激活项目之前，需要调整索引
        activeItemIndex.value--
      }

      uni.showToast({
        title: '删除成功',
        icon: 'success',
      })
    }

    showDeleteResponseConfirm.value = false
    deletingResponseIndex.value = -1
  } catch (error) {
    uni.showToast({
      title: '删除失败，请重试',
      icon: 'none',
    })
  }
}

// 切换项目操作按钮显示
const toggleItemActions = (index) => {
  if (activeItemIndex.value === index) {
    // 如果点击的是当前激活的项目，则隐藏操作按钮
    activeItemIndex.value = -1
  } else {
    // 否则显示当前项目的操作按钮
    activeItemIndex.value = index
  }
}

// 保存分析结果
const submitAnalysis = async () => {
  if (aiResponseData.value.length === 0) {
    uni.showToast({
      title: '没有可保存的分析结果',
      icon: 'none',
    })
    return
  }

  try {
    // 显示保存中状态
    uni.showLoading({
      title: '保存中...',
    })

    // 调用 flomo 接口保存分析结果
    const promises = aiResponseData.value.map(async (content) => {
      try {
        const response = await uni.request({
          url: 'https://flomoapp.com/iwh/MTE1MTQy/ad156344cdccbb9d22e39f424f502b83/',
          method: 'POST',
          header: {
            'Content-Type': 'application/json',
          },
          data: {
            content: content,
          },
        })

        if (response.statusCode === 200) {
          return { success: true, content }
        } else {
          return { success: false, content, error: `HTTP ${response.statusCode}` }
        }
      } catch (error) {
        return { success: false, content, error: error.message }
      }
    })

    const results = await Promise.all(promises)
    const successCount = results.filter((r) => r.success).length
    const totalCount = results.length

    uni.hideLoading()

    if (successCount === totalCount) {
      uni.showToast({
        title: `保存成功 (${successCount}/${totalCount})`,
        icon: 'success',
      })
    } else if (successCount > 0) {
      uni.showToast({
        title: `部分保存成功 (${successCount}/${totalCount})`,
        icon: 'none',
      })
    } else {
      uni.showToast({
        title: '保存失败，请重试',
        icon: 'none',
      })
      return
    }

    // 保存成功后清空分析结果，让用户可以开始新的记录
    setTimeout(() => {
      startNewEntry()
    }, 1500)
  } catch (error) {
    uni.hideLoading()
    console.error('保存分析结果失败：', error)
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
    })
  }
}

// 初始化数据
const initData = async () => {
  try {
    isLoadingTags.value = true
    
    // 调用云函数获取标签列表
    const response = await aiApi.flomoLabelManager({
      action: 'read',
      userId: 'test_user_001', // 使用测试用户ID
      options: {
        limit: 100, // 获取更多标签
        is_active: true,
      },
    })

    if (response.errCode === 0 && response.data) {
      tags.value = response.data
      console.log('标签加载成功：', tags.value.length)
    }
  } catch (error) {
    console.error('初始化数据失败：', error)
   
  } finally {
    isLoadingTags.value = false
  }
}

// 生命周期
onMounted(() => {
  initData()
})

// 处理 AI 响应文本中的标签高亮
const formatResponseText = (text) => {
  if (typeof text !== 'string') return text

  console.log('原始文本：', text)

  // 匹配 #标签名 的格式，将标签部分高亮
  // 支持中文字符，匹配#后面的标签名（包括中文、英文、数字）
  const result = text.replace(/#([\u4e00-\u9fa5\w]+)/g, '<span class="highlight-tag">#$1</span>')

  console.log('处理后的文本：', result)

  return result
}
</script>

<style lang="scss" scoped>
.flomo-page {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.status-bar-placeholder {
  height: var(--status-bar-height);
  background: #fff;
}

// 标签区域样式
.tags-section {
  background: #fff;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.tags-container {
  position: relative;
  transition: all 0.3s ease;
}

// 加载状态样式
.tags-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  padding: 40rpx;
  color: #666;

  i {
    font-size: 32rpx;
    color: #2196f3;
  }

  text {
    font-size: 28rpx;
  }
}

// 横向滚动标签
.tags-scroll {
  width: calc(100% - 60rpx); // 为展开按钮留出空间
  overflow: hidden;
}

.tags-scroll-content {
  display: flex;
  align-items: center;
  gap: 30rpx;
  padding-right: 20rpx;
  white-space: nowrap;
  min-width: max-content;
}

// 标签网格（展开后）
.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding-right: 60rpx; // 为展开按钮留出空间

  .tag-item,
  .add-tag-btn {
    margin-right: 0; // 在网格模式下不需要右边距
  }
}

.tag-item {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  margin-right: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  white-space: nowrap;
  flex-shrink: 0;
  position: relative;
  border: 2rpx solid transparent;

  text {
    font-size: 30rpx;
    color: #666;
    transition: color 0.3s ease;
  }

  &.active {
    background: #e3f2fd;
    border-color: #2196f3;

    text {
      color: #2196f3;
      font-weight: 500;
    }
  }

  &:hover {
    border-color: #e0e0e0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.95);
  }

  // 长按提示
  &::after {
    content: '';
    position: absolute;
    top: -2rpx;
    right: -2rpx;
    width: 8rpx;
    height: 8rpx;
    background: #999;
    border-radius: 50%;
    opacity: 0.6;
    transition: opacity 0.3s ease;
  }

  // 长按状态
  &.long-press {
    transform: scale(0.95);
    background: #e8f5e8;

    &::after {
      opacity: 1;
      background: #4caf50;
    }
  }
}

.add-tag-btn {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f0f0f0;
  border-radius: 20rpx;
  margin-right: 20rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
  border: 2rpx solid transparent;
  position: relative;

  i {
    font-size: 24rpx;
    color: #999;
  }

  text {
    font-size: 30rpx;
    color: #999;
  }

  &:hover {
    border-color: #2196f3;
    background: #e3f2fd;
    box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.2);
  }

  &:active {
    transform: scale(0.95);
    background: #e3f2fd;

    i,
    text {
      color: #2196f3;
    }
  }
}

// 展开/收起按钮
.expand-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  transition: all 0.3s ease;

  i {
    font-size: 24rpx;
    color: #666;
    transition: all 0.3s ease;
  }

  &:active {
    background: #e3f2fd;

    i {
      color: #2196f3;
    }
  }
}

// 日记输入区域样式
.diary-input-section {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.input-container {
  flex: 1;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

.diary-textarea {
  width: 100%;
  min-height: 400rpx;
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;

  &::placeholder {
    color: #999;
  }
}

// 操作按钮样式
.action-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.save-btn,
.clear-btn {
  flex: 1;
  max-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;

  i {
    font-size: 28rpx;
  }

  text {
    font-size: 30rpx;
    font-weight: 500;
  }
}

.save-btn {
  background: #2196f3;
  color: #fff;

  &.disabled {
    background: #ccc;
    color: #999;
  }

  &:not(.disabled):active {
    transform: scale(0.95);
    background: #1976d2;
  }
}

.clear-btn {
  background: #fff;
  color: #ff5722;
  border: 2rpx solid #ff5722;

  &:active {
    transform: scale(0.95);
    background: #ff5722;
    color: #fff;
  }
}

// 模态框样式
.add-tag-modal,
.edit-tag-modal,
.edit-response-modal {
  padding: 40rpx;
  background: #fff;
  border-radius: 14rpx;
}

// 模态框按钮loading样式
.modal-btn .loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  
  i {
    font-size: 24rpx;
    color: inherit;
  }
  
  text {
    font-size: 28rpx;
    color: inherit;
  }
}

.modal-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-content {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 30rpx;
  color: #333;
  background: #fff;

  &:focus {
    border-color: #2196f3;
    outline: none;
  }

  &::placeholder {
    color: #999;
  }
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  justify-content: space-between;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  text {
    font-size: 30rpx;
    font-weight: 500;
  }
}

.delete-btn {
  background: #ffebee;
  color: #f44336;
  flex: 1;

  i {
    font-size: 24rpx;
    margin-right: 8rpx;
  }

  &:active {
    background: #ffcdd2;
  }

  &.disabled {
    background: #ccc;
    color: #999;
  }
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  flex: 1;

  &:active {
    background: #e0e0e0;
  }
}

.confirm-btn {
  background: #2196f3;
  color: #fff;
  flex: 1;

  &.disabled {
    background: #ccc;
    color: #999;
  }

  &:not(.disabled):active {
    background: #1976d2;
  }
}



// 动画效果
.animate-fade-in-down {
  animation: fadeInDown 0.3s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// AI 响应区域样式
.ai-response-section {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.response-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.new-entry-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  background: #2196f3;
  color: #fff;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  i {
    font-size: 24rpx;
  }

  text {
    font-size: 28rpx;
    font-weight: 500;
  }

  &:active {
    transform: scale(0.95);
    background: #1976d2;
  }
}

.response-content {
  flex: 1;
  background: transparent;
  padding: 0;
}

.response-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
    transform: translateY(-2rpx);
  }

  // 点击提示
  &::after {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 8rpx;
    height: 8rpx;
    background: #ccc;
    border-radius: 50%;
    opacity: 0.6;
    transition: opacity 0.3s ease;
  }

  &.show-actions::after {
    opacity: 0;
  }
}

.item-content {
  margin-bottom: 20rpx;
}

.item-actions {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
  pointer-events: none;
  height: 0;
  overflow: hidden;
}

.response-item.show-actions .item-actions {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  height: auto;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;

  i {
    font-size: 24rpx;
  }

  &:active {
    transform: scale(0.9);
  }
}

.edit-btn {
  background: #e3f2fd;
  color: #2196f3;

  &:hover {
    background: #bbdefb;
  }

  &:active {
    background: #90caf9;
  }
}

.delete-btn {
  background: #ffebee;
  color: #f44336;

  &:hover {
    background: #ffcdd2;
  }

  &:active {
    background: #ef9a9a;
  }
}

.item-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
  text-align: left;
}

// 处理中状态
.processing {
  .response-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200rpx;
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
  text-align: center;
}

// 提交按钮样式
.submit-section {
  margin-top: 40rpx;
  padding: 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #4caf50;
  color: #fff;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  font-weight: 500;

  i {
    font-size: 28rpx;
  }

  text {
    font-size: 32rpx;
    font-weight: 500;
  }

  &:not(.disabled):active {
    transform: scale(0.95);
    background: #388e3c;
  }

  &.disabled {
    background: #ccc;
    color: #999;
    cursor: not-allowed;

    &:active {
      transform: none;
    }
  }
}

// 响应式适配
@media (max-width: 750rpx) {
  .tags-scroll-content {
    gap: 24rpx;
  }

  .tags-grid {
    gap: 16rpx;
  }

  .tag-item,
  .add-tag-btn {
    text {
      font-size: 28rpx;
    }
  }

  .diary-textarea {
    font-size: 30rpx;
    min-height: 350rpx;
  }

  .action-buttons {
    flex-direction: column;

    .save-btn,
    .clear-btn {
      max-width: none;
    }
  }

  .response-header {
    flex-direction: column;
    gap: 20rpx;
    align-items: flex-start;
  }

  .response-item {
    padding: 24rpx;
  }

  .item-content {
    margin-bottom: 16rpx;
  }

  .item-actions {
    justify-content: flex-end;
    height: auto;
  }

  .action-btn {
    width: 56rpx;
    height: 56rpx;
  }

  .submit-section {
    margin-top: 30rpx;
    padding: 24rpx;
  }

  .submit-btn {
    height: 80rpx;

    text {
      font-size: 30rpx;
    }
  }

  // 标签高亮样式
  .highlight-tag {
    color: #2196f3 !important;
    font-weight: 600 !important;
  }
}
</style>

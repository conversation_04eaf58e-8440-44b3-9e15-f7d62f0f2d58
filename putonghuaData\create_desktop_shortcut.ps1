# Create desktop shortcut for json_viewer.py

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$desktopPath = [Environment]::GetFolderPath("Desktop")
$shortcutName = "PutonghuaLearning.lnk"

$WScriptShell = New-Object -ComObject WScript.Shell
$shortcut = $WScriptShell.CreateShortcut("$desktopPath\$shortcutName")

$shortcut.TargetPath = "$scriptDir\run_json_viewer.bat"
$shortcut.WorkingDirectory = $scriptDir
$shortcut.Description = "Putonghua Learning Tool"

$shortcut.Save()

Write-Host "Desktop shortcut created: $desktopPath\$shortcutName"
Write-Host "Target: $scriptDir\run_json_viewer.bat"
